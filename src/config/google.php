<?php

declare(strict_types=1);

return [
    'project_id'    => env('GOOGLE_CLOUD_PROJECT_ID'),
    'pubsub'        => [
        'key_value'              => env('GCS_KEY_VALUE'),
        'topics'                 => [
            'fetcher_actions' => 'smart-crawler-fetched-data',
        ],
    ],
    'gemini' => [
        'location' => env('GEMINI_LOCATION'),
        'key_path' => env('GCP_SINGLE_KEY') ? base_path('keys/gcp/' . env('GCP_SINGLE_KEY')) : null,
        'base_url' => 'https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/google/models/%s:generateContent',
    ],
];
