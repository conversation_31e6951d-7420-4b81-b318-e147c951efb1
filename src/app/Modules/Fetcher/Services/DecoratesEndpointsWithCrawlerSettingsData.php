<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Services;

use App\Classes\ValueObjects\CrawlerSettingForEndpointObject;
use App\Classes\ValueObjects\EndpointWithCrawlerSettingObject;
use App\Models\PublisherEndpoint;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use RuntimeException;

class DecoratesEndpointsWithCrawlerSettingsData {
    /**
     * @param Collection<PublisherEndpoint>|PublisherEndpoint $endpoints
     *
     * @throws RuntimeException
     *
     * @return Collection<>
     */
    public function execute(PublisherEndpoint | Collection $endpoints): Collection {
        if ($endpoints instanceof PublisherEndpoint) {
            $endpoints = new EloquentCollection([$endpoints]);
        }

        $endpoints->loadMissing('crawlerSetting');

        return $endpoints->map(function (PublisherEndpoint $endpoint) {
            $crawler = $endpoint->crawlerSetting;

            return new EndpointWithCrawlerSettingObject(
                $endpoint,
                new CrawlerSettingForEndpointObject($crawler)
            );
        });
    }
}
