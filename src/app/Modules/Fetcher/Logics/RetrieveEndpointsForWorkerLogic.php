<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Logics;

use App\Classes\StructuredData\StructuresDataForApi;
use App\Classes\StructuredData\Transformers\EndpointsForFetcherTransformer;
use App\Modules\Fetcher\Services\DecoratesEndpointsWithCrawlerSettingsData;
use App\Repositories\PublisherCrawlerSettingRepository;
use App\Repositories\PublisherEndpointRepository;

class RetrieveEndpointsForWorkerLogic {
    public function __construct(
        private PublisherCrawlerSettingRepository $publisherCrawlerSettingsRepository,
        private StructuresDataForApi $structure,
        private DecoratesEndpointsWithCrawlerSettingsData $decorateEndpointsWithCrawlerSettingsData,
        private PublisherEndpointRepository $publisherEndpointsRepository,
    ) {
    }

    public function execute(string $workerId): array {
        $endpointsWithCrawlerSettings = $this->publisherEndpointsRepository->getCrawlerSettingsWithEndpoints($workerId);
        $decoratedEndpoints           = $this->decorateEndpointsWithCrawlerSettingsData->execute($endpointsWithCrawlerSettings);

        return $this->structure->returnMany(
            $decoratedEndpoints,
            new EndpointsForFetcherTransformer()
        );
    }
}
