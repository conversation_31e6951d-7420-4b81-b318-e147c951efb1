<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Logics;

use App\Events\ArticleDataIsFetched;
use App\Exceptions\APIException;
use App\Services\ParserAdderClient;
use Throwable;

class PassArticleDataToParserAdderServiceLogic {
    public function __construct(
        private ParserAdderClient $parserAdderClient,
    ) {
    }

    public function execute(int $publisherId, int $channelId, array $articleData, bool $isRss, ?string $customPrompt, bool $useHeadlessBrowser): void {

        try {
            foreach ($articleData as $article) {
                if (app()->env === 'local') {
                    $this->parserAdderClient->postArticleDataToParserAdderService(
                        $publisherId,
                        $channelId,
                        $article,
                        $isRss,
                        $customPrompt,
                        $useHeadlessBrowser
                    );

                    return;
                }

                event(new ArticleDataIsFetched(
                    $publisherId,
                    $channelId,
                    $article,
                    $isRss,
                    $customPrompt ?? '',
                    $useHeadlessBrowser
                ));
            }

        } catch (Throwable $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }
}
