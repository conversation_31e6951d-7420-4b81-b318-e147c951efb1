<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Logics;

use App\Classes\StructuredData\StructuresDataForApi;
use App\Classes\StructuredData\Transformers\EndpointsForFetcherTransformer;
use App\Modules\Fetcher\Services\DecoratesEndpointsWithCrawlerSettingsData;
use App\Repositories\PublisherCrawlerSettingRepository;
use App\Repositories\PublisherEndpointRepository;

class RetrieveEndpointForLocalFetcherLogic {
    public function __construct(
        private PublisherCrawlerSettingRepository $publisherCrawlerSettingsRepository,
        private StructuresDataForApi $structure,
        private DecoratesEndpointsWithCrawlerSettingsData $decorateEndpointsWithCrawlerSettingsData,
        private PublisherEndpointRepository $publisherEndpointsRepository,
    ) {
    }

    public function execute(string $endpointId): array {
        $endpointsWithCrawlerSettings = $this->publisherEndpointsRepository->getOneCrawlerSettingWithEndpointByEndpointId($endpointId);
        $decoratedEndpoints           = $this->decorateEndpointsWithCrawlerSettingsData->execute($endpointsWithCrawlerSettings);

        return $this->structure->returnMany(
            $decoratedEndpoints,
            new EndpointsForFetcherTransformer()
        );
    }
}
