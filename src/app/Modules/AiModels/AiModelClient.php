<?php

declare(strict_types=1);

namespace App\Modules\AiModels;

use App\Classes\Constants\ServerParameters;
use App\Classes\Interfaces\AiModel;
use App\Classes\ValueObjects\GeminiAiModelObject;
use App\Classes\ValueObjects\OpenAiModelObject;
use App\Enums\AiModels;
use App\Exceptions\AiClientException;
use App\Exceptions\APIException;
use App\Modules\AiModels\Services\LogsAiModelEstimatedCost;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;
use Throwable;

class AiModelClient {
    public function __construct(
        private Client $client,
        private LogsAiModelEstimatedCost $logsAiModelEstimatedCost,
        private GeminiAccessTokenProvider $geminiAccessTokenProvider,

    ) {
    }

    public function ask(string $systemPrompt, string $userPrompt): string {

        $projectId = config('google.project_id');
        $location  = config('google.gemini.location');
        $model     = AiModels::GEMINI_2_0_FLASH;
        $modelUrl  = sprintf(
            config('google.gemini.base_url'),
            $location,
            $projectId,
            $location,
            $model->value
        );

        $geminiModel = new GeminiAiModelObject($this->geminiAccessTokenProvider->execute(), $modelUrl, $userPrompt, $systemPrompt, $model);

        try {
            $response = $this->makeRequestWithRetry($geminiModel, 1);
            $result   = json_decode($response->getBody()->getContents(), true);
            $this->logsAiModelEstimatedCost->execute(
                $geminiModel,
                $result['usageMetadata']['promptTokenCount'],
                $result['usageMetadata']['candidatesTokenCount']
            );

            return $result['candidates'][0]['content']['parts'][0]['text'];
        } catch (Throwable $e) {
            Log::warning('Gemini failed, switching to GPT. Error: ' . $e->getMessage());
        }

        $gptModel = new OpenAiModelObject(config('openai.api_key'), config('openai.base_url'), $userPrompt, $systemPrompt);

        try {
            $response = $this->makeRequestWithRetry($gptModel, 1);
            $result   = json_decode($response->getBody()->getContents(), true);
            $this->logsAiModelEstimatedCost->execute(
                $gptModel,
                $result['usage']['prompt_tokens'],
                $result['usage']['completion_tokens']
            );

            return $result['choices'][0]['message']['content'];
        } catch (Throwable $e) {
            throw new AiClientException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, 'Both Gemini and GPT failed. Error: ' . $e->getMessage());
        }
    }

    private function makeRequestWithRetry(AiModel $aiModel, int $retryTimes = 1): ResponseInterface {
        $attempt   = 0;
        $lastError = null;
        while ($attempt <= $retryTimes) {
            try {
                return $this->makeRequest($aiModel);
            } catch (Throwable $e) {
                $attempt++;
                $lastError = $e;
                Log::warning("Attempt {$attempt} failed for " . get_class($aiModel) . ': ' . $e->getMessage());
                usleep(250000);
            }
        }

        throw new APIException($lastError?->getCode() ?? 500, sprintf('Failed to get response from %s after %d retries. Last error: %s', get_class($aiModel), $retryTimes, $lastError?->getMessage() ?? 'Unknown error'));
    }

    private function makeRequest(AiModel $aiModel): ResponseInterface {
        try {
            $response =  $this->client->request(ServerParameters::HTTP_METHOD_POST, $aiModel->getModelUrl(), [
                'headers' => [
                    'Authorization' => sprintf('Bearer %s', $aiModel->getApiKey()),
                    'Content-Type'  => 'application/json',
                ],
                'json' => $aiModel->getModelParameters(),
            ]);

            return $response;
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }
}
