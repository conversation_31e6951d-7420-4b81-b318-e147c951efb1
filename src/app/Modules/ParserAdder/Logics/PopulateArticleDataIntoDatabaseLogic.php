<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Logics;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use App\Classes\ValueObjects\ArticleObject;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Exceptions\ArticleAdderException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Services\CreatesArticle;
use App\Modules\ParserAdder\Services\CreatesArticleMedia;
use App\Modules\ParserAdder\Services\CreatesPrediction;
use App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle;
use App\Modules\ParserAdder\Services\PopulatesArticleWordCount;
use App\Modules\ParserAdder\Services\UpdatesArticle;
use App\Repositories\ArticleRepository;
use App\Repositories\PredictionsRepository;
use App\Services\NewswavOutboxClient;
use Carbon\Carbon;
use Throwable;

class PopulateArticleDataIntoDatabaseLogic {
    public function __construct(
        private GeneratesUniqueIdForArticle $generatesUniqueIdForArticle,
        private CreatesArticleMedia $createsArticleMedia,
        private PopulatesArticleWordCount $populatesArticleWordCount,
        private CreatesArticle $createsArticle,
        private UpdatesArticle $updatesArticle,
        private CreatesPrediction $createsPrediction,
        private ArticleRepository $articleRepository,
        private PredictionsRepository $predictionRepository,
        private NewswavOutboxClient $newswavOutboxClient,
        private ContentHelper $contentHelper,
    ) {
    }

    public function execute(ParsedArticleObject $articleParsed, int $publisherId, int $channelId): void {

        try {
            $uniqueId            = $this->generatesUniqueIdForArticle->execute($articleParsed->getArticleId(), $channelId);
            $mediaIds            = $this->createsArticleMedia->execute($articleParsed->getCoverImage(), $articleParsed->getMedia(), $publisherId, $channelId);

            $this->populatesArticleWordCount->execute($uniqueId, $articleParsed->getFullContent());

            if ($articleParsed->getPublishedDate() === null || in_array($channelId, Parser::CHANNELS_TO_EXCLUDE_PUBLISHED_DATE, true)) {
                $articleParsed->setPublishedDate(Carbon::now()->toDateString());
            }

            $articleObject = new ArticleObject(
                $articleParsed->getArticleId(),
                $uniqueId,
                $channelId,
                $articleParsed->getTitle(),
                $articleParsed->getDescription(),
                $articleParsed->getFullContent(),
                $articleParsed->getAuthor(),
                $articleParsed->getPublishedDate(),
                $articleParsed->getModifiedDate(),
                $articleParsed->getCanonicalURL(),
                $articleParsed->getCanonicalURL(),
                implode(',', $mediaIds),
                $this->contentHelper->slugifyTitle($articleParsed->getTitle()) . '-' . $uniqueId,
                $articleParsed->getContentMd5(),
            );

            if ($this->articleRepository->findWhere(['articleID' => $articleParsed->getArticleId(), 'channelID' => $channelId]) === null) {
                $article = $this->createsArticle->execute($articleObject);

                if (in_array($publisherId, Parser::CHANNELS_TO_BYPASS_PREDICTION, true) === true) {
                    $this->createsPrediction->execute($article);

                    $articleDataWithPredictionData = $this->predictionRepository->getArticleDataWithPredictionData($uniqueId);

                    $articleDataWithPredictionDataObject = new ArticleDataWithPredictionDataObject(
                        $articleDataWithPredictionData
                    );
                    $this->newswavOutboxClient->emitMessage('feed', $articleDataWithPredictionDataObject, 'contentId', 'internal.static.prediction');
                }
            } else {
                $this->updatesArticle->execute($articleObject);
            }
        } catch (Throwable $exception) {
            throw new ArticleAdderException($exception->getCode(), $exception->getMessage() . ' Publisher ID: ' . $publisherId . ' Channel ID: ' . $channelId . ' Unique ID: ' . $uniqueId ?? null);
        }

    }
}
