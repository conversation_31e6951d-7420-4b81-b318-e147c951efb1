<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Logics;

use App\Classes\Constants\AiPrompts;
use App\Classes\Constants\Parser;
use App\Modules\AiModels\AiModelClient;
use App\Modules\ParserAdder\Services\SanitizesParsedContent;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class ParseArticleDataLogic {
    public function __construct(
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private ValidatesParsedContent $validatesParsedContent,
        private SanitizesParsedContent $sanitizesParsedContent,
        private AiModelClient $aiModelClient,
    ) {
    }

    public function execute(Request $request): array {
        try {
            $publisherId        = $request->get('publisher_id');
            $rawData            = $request->get('article_data');
            $isRss              = $request->get('is_rss');
            $customPrompt       = $request->get('custom_prompt', '');
            $useHeadlessBrowser = $request->get('use_headless_browser', false);

            Log::info('Starting article parsing process', [
                'publisher_id'      => $publisherId,
                'raw_data'          => $rawData,
                'is_rss'            => $isRss,
            ]);

            if ($isRss === false) {
                Log::info('Fetching raw HTML content for non-RSS article');
                $rawData = $useHeadlessBrowser === true ? $this->headlessBrowserService->getRawHtmlOrRss($rawData)->getRawContent() : $this->bypassCloudflareService->getRawHtmlOrRss($rawData)->getRawContent();
            }

            $userPrompt    = str_replace(':raw_content', $rawData, $customPrompt === null ? AiPrompts::EXTRACT_ARTICLE_CONTENTS_FROM_RAW_CONTENT_USER_PROMPT : $customPrompt);
            $parsedContent = $this->extractJson($this->aiModelClient->ask(AiPrompts::EXTRACT_ARTICLE_CONTENTS_FROM_RAW_CONTENT_SYSTEM_PROMPT, $userPrompt));
            Log::info('Content parsed successfully.');

            $validatedContent = $this->validatesParsedContent->execute($parsedContent, $publisherId);
            Log::info('Content validation completed');

            $sanitizedContent = $this->sanitizesParsedContent->execute($validatedContent, $publisherId);
            Log::info('Content sanitization completed');

            $sanitizedContent[Parser::CONTENT_MD5] = md5($rawData);
            Log::info('Article parsing process completed successfully');

            return $sanitizedContent;
        } catch (Throwable $exception) {
            Log::error('Error in ParseArticleDataLogic: Failed to parse and add article', [
                'error'        => $exception->getMessage(),
                'publisher_id' => $publisherId,
                'is_rss'       => $isRss,
            ]);

            return [];
        }

    }

    private function extractJson($input): array {
        $input = trim($input);
        $input = preg_replace('/^```(?:json)?\s*|\s*```$/i', '', $input);

        return json_decode($input, true);
    }
}
