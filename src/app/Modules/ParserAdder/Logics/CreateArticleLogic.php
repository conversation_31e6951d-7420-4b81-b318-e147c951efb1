<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Logics;

use App\Classes\Constants\Parser;
use App\Classes\StructuredData\StructuresDataForApi;
use App\Classes\StructuredData\Transformers\ArticleDataWithPredictionDataTransformer;
use App\Classes\ValueObjects\ArticleObject;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Services\CreatesArticle;
use App\Modules\ParserAdder\Services\CreatesArticleMedia;
use App\Modules\ParserAdder\Services\CreatesPrediction;
use App\Modules\ParserAdder\Services\DecoratesArticleDataWithPredictionData;
use App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle;
use App\Modules\ParserAdder\Services\PopulatesArticleWordCount;
use App\Modules\ParserAdder\Services\UpdatesArticle;
use App\Repositories\ArticleRepository;
use App\Repositories\PredictionsRepository;
use App\Services\NewswavOutboxClient;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Throwable;

class CreateArticleLogic {
    public function __construct(
        private GeneratesUniqueIdForArticle $generatesUniqueIdForArticle,
        private CreatesArticleMedia $createsArticleMedia,
        private PopulatesArticleWordCount $populatesArticleWordCount,
        private CreatesArticle $createsArticle,
        private UpdatesArticle $updatesArticle,
        private CreatesPrediction $createsPrediction,
        private ArticleRepository $articleRepository,
        private PredictionsRepository $predictionRepository,
        private NewswavOutboxClient $newswavOutboxClient,
        private DecoratesArticleDataWithPredictionData $decoratesArticleDataWithPredictionData,
        private StructuresDataForApi $structure,
        private ContentHelper $contentHelper,
    ) {
    }

    public function execute(ParsedArticleObject $articleParsed, int $publisherId, int $channelId): void {

        try {
            Log::info('Starting article data processing');
            $uniqueId            = $this->generatesUniqueIdForArticle->execute($articleParsed->getArticleId(), $channelId);

            $mediaIds            = $this->createsArticleMedia->execute($articleParsed->getCoverImage(), $articleParsed->getMedia(), $publisherId, $channelId);
            Log::info('Media inserted into database');

            $this->populatesArticleWordCount->execute($uniqueId, $articleParsed->getFullContent());
            Log::info('Article word count inserted into database');

            if ($articleParsed->getPublishedDate() === null || in_array($channelId, Parser::CHANNELS_TO_EXCLUDE_PUBLISHED_DATE, true)) {
                $articleParsed->setPublishedDate(Carbon::now()->toDateString());
            }

            $articleObject = new ArticleObject(
                $articleParsed->getArticleId(),
                $uniqueId,
                $channelId,
                $articleParsed->getTitle(),
                $articleParsed->getDescription(),
                $articleParsed->getFullContent(),
                $articleParsed->getAuthor(),
                $articleParsed->getPublishedDate(),
                $articleParsed->getModifiedDate(),
                $articleParsed->getCanonicalURL(),
                $articleParsed->getCanonicalURL(),
                implode(',', $mediaIds),
                $this->contentHelper->slugifyTitle($articleParsed->getTitle()) . '-' . $uniqueId,
                $articleParsed->getContentMd5(),
            );

            if ($this->articleRepository->findWhere(['articleID' => $articleParsed->getArticleId(), 'channelID' => $channelId]) === null) {
                $article = $this->createsArticle->execute($articleObject);
                Log::info('Article inserted into database');

                if (in_array($publisherId, Parser::CHANNELS_TO_BYPASS_PREDICTION, true) === true) {
                    $this->createsPrediction->execute($article);
                    Log::info('Prediction inserted into database');
                }

                $articleDataWithPredictionData = $this->predictionRepository->getArticleDataWithPredictionData($uniqueId);
                $decoratedData                 = $this->decoratesArticleDataWithPredictionData->execute($articleDataWithPredictionData)->first();

                Log::info('Emitting outbox message');
                $this->newswavOutboxClient->emitMessage('feed', $this->structure->returnOne(
                    $decoratedData,
                    new ArticleDataWithPredictionDataTransformer()
                ), 'contentId', 'internal.static.prediction');
                Log::info('Outbox message emitted');

            } else {
                $this->updatesArticle->execute($articleObject);
                Log::info('Article updated in database');
            }
        } catch (Throwable $exception) {
            Log::error('Error in CreateArticleLogic: Failed to add article data to database', [
                'error'        => $exception->getMessage(),
                'publisher_id' => $publisherId,
                'channel_id'   => $channelId,
                'unique_id'    => $uniqueId ?? null,
            ]);
        }

    }
}
