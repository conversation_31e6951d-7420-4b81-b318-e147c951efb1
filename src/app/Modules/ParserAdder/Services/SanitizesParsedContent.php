<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\Constants\Parser;
use App\Helpers\ContentHelper;
use App\Repositories\PublisherRepository;
use RuntimeException;
use Throwable;

class SanitizesParsedContent {
    public function __construct(
        private PublisherRepository $publisherRepository,
        private ContentHelper $contentHelper,
    ) {
    }

    public function execute(array $parsedContent, int $publisherId): array {
        try {
            $parsedContent['description']    = $this->sanitizeDescription($parsedContent['description'] ?? '');
            $parsedContent['full_content']   = $this->sanitizeContent($parsedContent['full_content'], $parsedContent[Parser::COVER_IMAGE_URL], $publisherId, $parsedContent['title'], $parsedContent['link']);
            $parsedContent['link']           = $this->contentHelper->getCanonicalUrl($parsedContent['link']);

            return $parsedContent;
        } catch (Throwable $exception) {
            throw new RuntimeException('Error in SanitizesParsedContent: Failed to sanitize content', $exception->getCode(), $exception);
        }
    }

    protected function addGaTrackingIntoContent(string $content, int $publisherId, string $link, string $title): string {
        $iframes = '';
        $gaid    = $this->publisherRepository->find($publisherId)?->ga_id;

        if ($gaid) {
            if (preg_match('/^UA-\d{4,10}-\d+$/', $gaid)) {
                $encodedPath = urlencode($this->getPath($link));
                $iframes .= '<iframe src="https://cdn.newswav.com/global-track5.html?path=' . $encodedPath
                    . '&ga-id=' . $gaid
                    . '&title=' . urlencode($title)
                    . '&utm_source=Newswav&utm_medium=App" height="0" frameBorder="0"></iframe>';
            } else {
                $encodedPath = urlencode($this->getPath($link));
                $iframes .= '<iframe src="https://cdn.newswav.com/ga4-track.html?path=' . $encodedPath
                    . '&ga-id=' . $gaid
                    . '&title=' . urlencode($title)
                    . '&utm_source=Newswav&utm_medium=App" height="0" frameBorder="0"></iframe>';

                $debugGA = 'https://cdn.newswav.com/ga4-v2-track.html?path=' . urlencode($link)
                    . '&ga-id=G-FG1LT8DMNS'
                    . '&title=' . urlencode($title)
                    . '&utm_source=ga4-v2&utm_medium=Newswav-App&utm_campaign=';

                $iframes .= '<iframe src="' . $debugGA . '" height="0" frameBorder="0"></iframe>';
            }
        }

        return $iframes . $content;
    }

    protected function sanitizeDescription(string $description): string {
        $words = preg_split('/\s+/', strip_tags($description));
        if (count($words) > Parser::MAX_DESCRIPTION_WORD_COUNT) {
            $description = implode(' ', array_slice($words, 0, Parser::MAX_DESCRIPTION_WORD_COUNT)) . '...';
        }

        return $description;
    }

    protected function sanitizeContent(string $content, array $coverImage, int $publisherId, string $title, string $link): string {
        $content = $this->addFirstImage($content, $coverImage, $title);

        $content       = preg_replace('#<(script|style)\b[^>]*>(.*?)</\1>#is', '', $content);
        $content       = preg_replace('/[\t\n\r]+/', '', $content);
        $androidScript = '<script type="text/javascript">function showImageDetail(position) { Android.showImage(position); }</script>';
        $content       = $content . $androidScript;

        return $this->insertAdsIntoContent($this->addGaTrackingIntoContent($content, $publisherId, $link, $title));
    }

    protected function addFirstImage(string $content, array $coverImage, string $title) {
        if ( ! preg_match('/<img\b[^>]*>/i', $content) && $coverImage['url']) {
            $escapedUrl     = htmlspecialchars($coverImage['url'], ENT_QUOTES);
            $escapedCaption = htmlspecialchars($coverImage['caption'] ?? '', ENT_QUOTES);
            $escapedTitle   = htmlspecialchars($title, ENT_QUOTES);

            $figure = "<figure style='text-align: center;'>";
            $figure .= "<img onclick=\"showImageDetail('0')\""
                . " src='https://imgproxy.newswav.com/1000x0,q50=/{$escapedUrl}'"
                . " style='width:100%' class='fullWidthImg'"
                . ' srcset="'
                . "https://imgproxy.newswav.com/400x0,q50=/{$escapedUrl} 400w, "
                . "https://imgproxy.newswav.com/768x0,q50=/{$escapedUrl} 768w, "
                . "https://imgproxy.newswav.com/1000x0,q50=/{$escapedUrl} 1000w\""
                . " alt='{$escapedTitle}'>";
            $figure .= "<figcaption> {$escapedCaption} </figcaption>";
            $figure .= '</figure>';

            return $figure . $content;
        }

        return $content;
    }

    protected function insertAdsIntoContent(string $content): string {
        $parts      = preg_split('/(<\/p>)/i', $content, -1, PREG_SPLIT_DELIM_CAPTURE);
        $blocks     = array_chunk($parts, 2);
        $blockCount = count($blocks);

        if ($blockCount === 1) {
            return implode('', $blocks[0]) . '<!--AD-->';
        }

        if ($blockCount === 2) {
            return implode('', $blocks[0]) . '<!--AD-->' . implode('', $blocks[1]);
        }

        $adsToInsert = max(1, min(Parser::MAX_ADS_COUNT, $blockCount - 1));
        $interval    = floor($blockCount / ($adsToInsert + 1));

        $newContent = '';
        foreach ($blocks as $index => $blockPair) {
            $newContent .= implode('', $blockPair);

            if (
                $interval > 0
                && $adsToInsert > 0
                && ($index + 1) % $interval === 0
                && ($index + 1) !== $blockCount
            ) {
                $newContent .= '<!--AD-->';
                $adsToInsert--;
            }
        }

        return $newContent;
    }

    protected function getPath(string $url): string {
        return parse_url($url, PHP_URL_PATH) ?? '';
    }
}
