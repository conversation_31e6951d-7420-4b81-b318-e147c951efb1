<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Helpers\MediaHelper;
use App\Repositories\ArticleRepository;
use App\Repositories\MediaRepository;
use App\Repositories\PublisherEndpointRepository;
use App\Repositories\ThumbnailRepository;

class CreatesArticleMedia {
    public function __construct(
        private ArticleRepository $articleRepository,
        private MediaRepository $mediaRepository,
        private PublisherEndpointRepository $publisherEndpointsRepository,
        private ThumbnailRepository $thumbnailRepository,
        private RetrievesImageProxyLinks $retrievesImageProxyLinks,
        private MediaHelper $mediaHelper,
    ) {
    }

    public function execute(array $coverImage, array $mediaArray, int $publisherId, int $channelId): array {
        $hasProxyImage = $this->publisherEndpointsRepository->getHasProxyImage($publisherId, $channelId);

        $mediaIds = [];

        $mediaIds[] = $this->handleMedia($coverImage, $hasProxyImage);

        foreach ($mediaArray as $media) {
            $mediaIds[] = $this->handleMedia($media, $hasProxyImage);
        }

        return $mediaIds;
    }

    private function handleMedia(array $media, bool $hasProxyImage): int {
        $mediaId = $this->insertMedia($media, $hasProxyImage);
        if ($mediaId) {
            $this->insertThumbnail($mediaId, $hasProxyImage);
        }

        return $mediaId;
    }

    private function insertThumbnail(int $mediaId, bool $hasProxyImage): void {
        if ($mediaId === null) {
            return;
        }
        $media     = $this->mediaRepository->find($mediaId);
        $imageUrls = [
            'square' => $media->url,
            'wide'   => $media->url,
        ];

        if ($hasProxyImage === true) {
            $imageUrls = $this->retrievesImageProxyLinks->execute(true, $media->url);
        }

        if ($this->mediaHelper->isMediaUrlValid($imageUrls['square']) === false || $this->mediaHelper->isMediaUrlValid($imageUrls['wide']) === false) {
            return;
        }

        $this->thumbnailRepository->create([
            'squareUrl'          => $imageUrls['square'],
            'wideUrl'            => $imageUrls['wide'],
            'squareImageSize'    => config('image_proxy.square_size'),
            'wideImageWidth'     => config('image_proxy.wide_width'),
            'wideImageHeight'    => config('image_proxy.wide_height'),
            'mediaOwnerType'     => 'media',
            'mediaOwnerId'       => $mediaId,
        ]);
    }

    private function insertMedia(array $media, bool $hasProxyImage): ?int {

        if ($hasProxyImage === true) {
            $media['url'] = $this->retrievesImageProxyLinks->execute(false, $media['url'])['regular'];
        }

        $existingMedia = $this->mediaRepository->findWhere(['url' => $media['url']]);
        if ($existingMedia !== null) {
            return $existingMedia->id;
        }

        if ($this->mediaHelper->isMediaUrlValid($media['url']) === false) {
            return null;
        }

        $width  = 0;
        $height = 0;

        if ($this->mediaHelper->isVideoUrl($media['url']) === false) {
            $mediaSize = $this->mediaHelper->getImageSize($media['url']);
            $width     = $mediaSize['width'];
            $height    = $mediaSize['height'];
        }

        $createdMedia = $this->mediaRepository->create([
            'caption'     => $media['caption'] ?? '',
            'url'         => $media['url'],
            'originalURL' => '',
            'wpURL'       => '',
            'imageWidth'  => $width ?? 0,
            'imageHeight' => $height ?? 0,
        ]);

        return $createdMedia->id;
    }
}
