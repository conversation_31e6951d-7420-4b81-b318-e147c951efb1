<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use App\Models\Prediction;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use RuntimeException;

class DecoratesArticleDataWithPredictionData {
    /**
     * @throws RuntimeException
     *
     * @return Collection<>
     */
    public function execute(Prediction | Collection $predictionData): Collection {
        if ($predictionData instanceof Prediction) {
            $predictionData = new EloquentCollection([$predictionData]);
        }

        $predictionData->loadMissing('article', 'article.channel', 'publisher');

        return $predictionData->map(function (Prediction $predictionData) {
            $article   = $predictionData->article;
            $channel   = $article->channel;
            $publisher = $predictionData->publisher;

            return new ArticleDataWithPredictionDataObject(
                $predictionData,
                $article,
                $channel,
                $publisher
            );
        });
    }
}
