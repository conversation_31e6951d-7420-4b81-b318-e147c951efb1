<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\Constants\Parser;
use App\Helpers\MediaHelper;
use App\Repositories\PublisherRepository;
use Carbon\Carbon;
use RuntimeException;

class ValidatesParsedContent {
    public function __construct(
        private PublisherRepository $publisherRepository,
        private MediaHelper $mediaHelper,
    ) {
    }

    public function execute(array $parsedContent, int $publisherId): array {
        $validated = [];

        foreach (Parser::REQUIRED_FIELDS as $field) {
            if (empty($parsedContent[$field])) {
                throw new RuntimeException("Error in ValidatesParsedContent: Missing required field: {$field}.");
            }
        }

        foreach (Parser::DEFAULT_PARSED_CONTENT_FIELDS as $key => $defaultField) {
            $validated[$key] = match ($key) {
                Parser::ARTICLE_ID      => $this->validateArticleId($parsedContent),
                Parser::DESCRIPTION     => $this->validateDescription($parsedContent),
                Parser::AUTHOR          => $this->validateAuthor($parsedContent, $publisherId, $defaultField),
                Parser::PUBLISHED_DATE  => $this->validatePublishedDate($parsedContent),
                Parser::MODIFIED_DATE   => $this->validateModifiedDate($parsedContent),
                Parser::COVER_IMAGE_URL => $this->validateCoverImage($parsedContent, $defaultField),
                Parser::MEDIA           => $this->validateMedia($parsedContent[$key] ?? []),
                default                 => $parsedContent[$key] ?? $defaultField,
            };
        }

        return $validated;
    }

    private function validateArticleId(array $content): int {
        return (int) $content[Parser::ARTICLE_ID];
    }

    private function validateDescription(array $content): string {
        if (empty($content[Parser::DESCRIPTION]) === false) {
            return $content[Parser::DESCRIPTION];
        }

        return substr(strip_tags($content[Parser::FULL_CONTENT]), 0, 150) . '...';
    }

    private function validateAuthor(array $content, int $publisherId, string $defaultName): string {
        if (empty($content[Parser::AUTHOR]) === false) {
            return $content[Parser::AUTHOR];
        }

        return ($publisher = $this->publisherRepository->findWhere(['id' => $publisherId])) === null
        ? $defaultName
        : $publisher->name;
    }

    private function validatePublishedDate(array $content): string {
        return $content[Parser::PUBLISHED_DATE] ? $content[Parser::PUBLISHED_DATE] : Carbon::now()->toDateString();
    }

    private function validateModifiedDate(array $content): string {
        return $content[Parser::MODIFIED_DATE] ? $content[Parser::MODIFIED_DATE] : Carbon::now()->toDateString();
    }

    private function validateCoverImage(array $content, array $default): array {
        if (empty($content[Parser::COVER_IMAGE_URL]['url']) === false) {
            return [
                'url'     => $content[Parser::COVER_IMAGE_URL]['url'],
                'caption' => $content[Parser::COVER_IMAGE_URL]['caption'] ?? null,
            ];
        }

        if (empty($content[Parser::MEDIA]) === false) {
            foreach ($content[Parser::MEDIA] as $media) {
                if (empty($media['url']) === false && ($this->mediaHelper->isImageUrl($media['url']) || $this->mediaHelper->isVideoUrl($media['url']))) {
                    return [
                        'url'     => $media['url'],
                        'caption' => $media['caption'] ?? null,
                    ];
                }
            }
        }

        return $default;
    }

    private function validateMedia(array $media): array {
        $validated = [];

        foreach ($media as $item) {
            if (empty($item['url'])) {
                continue;
            }

            $validated[] = [
                'url'     => $item['url'],
                'caption' => $item['caption'] ?? null,
            ];
        }

        return $validated;
    }
}
