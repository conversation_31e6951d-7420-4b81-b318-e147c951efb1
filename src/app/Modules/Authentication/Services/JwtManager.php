<?php

declare(strict_types=1);

namespace App\Modules\Authentication\Services;

use App\Classes\ValueObjects\JwtUserData;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class JwtManager {
    public const ALGORITHM = 'HS256';

    public function encode(array $data): string {
        $key       = config('app.jwt_key');
        $tokenData = [
            'iss' => 'https://newswav.com/',
            'aud' => 'nw-client',
            'iat' => time(),
            'exp' => time() + (6 * 360 * 24 * 60 * 60),
            ...$data,
        ];

        return JWT::encode($tokenData, $key, self::ALGORITHM);
    }

    public function decode(string $token): JwtUserData {
        $key     = config('app.jwt_key');
        $decoded = JWT::decode($token, new Key($key, self::ALGORITHM));

        return JwtUserData::createFromArray((array) $decoded);
    }
}
