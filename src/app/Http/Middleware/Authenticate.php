<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\UnauthorisedException;
use App\Modules\Authentication\Services\JwtManager;
use Closure;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class Authenticate {
    public const TOKEN_HEADER_KEY = 'nwtoken';

    public function __construct(private JwtManager $jwtManager) {
    }

    /**
     * @throws UnauthorisedException
     */
    public function handle(Request $request, Closure $next): JsonResponse {
        $nwtoken = $request->header(self::TOKEN_HEADER_KEY);
        if ($nwtoken === null) {
            throw new UnauthorisedException();
        }

        try {
            $decodedUserData = $this->jwtManager->decode($nwtoken);
            $request->attributes->add(['user_data' => $decodedUserData->toArray()]);
        } catch (Exception) {
            throw new UnauthorisedException();
        }

        return $next($request);
    }
}
