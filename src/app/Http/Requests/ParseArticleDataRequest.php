<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ParseArticleDataRequest extends FormRequest {
    public function rules(): array {
        return [
            'publisher_id'         => 'required|integer',
            'channel_id'           => 'required|integer',
            'article_data'         => 'required|string',
            'is_rss'               => 'required|boolean',
            'custom_prompt'        => 'nullable|string',
            'use_headless_browser' => 'nullable|boolean',
        ];
    }
}
