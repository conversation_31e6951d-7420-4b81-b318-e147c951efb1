<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider {
    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void {
        $this->routes(function (): void {
            Route::middleware('api')
                ->group(base_path('routes/api.php'));
        });
    }
}
