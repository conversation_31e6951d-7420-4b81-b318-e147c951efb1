<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Classes\Services\Criteria\CrawlerSettingsWithEndpointsForWorker;
use App\Classes\Services\Criteria\CrawlerSettingWithEndpointForLocalFetcher;
use App\Classes\Services\Criteria\HasProxyImageForPublisher;
use App\Classes\ValueObjects\EndpointObject;
use App\Models\PublisherEndpoint;
use Illuminate\Database\Eloquent\Collection;

/**
 * @extends BaseRepository<PublisherEndpoint>
 */
class PublisherEndpointRepository extends BaseRepository {
    public function __construct(PublisherEndpoint $model) {
        parent::__construct($model);
    }

    public function getCrawlerSettingsWithEndpoints(string $workerId): Collection {
        return $this->getBuilderBasedOnCriteria(
            new CrawlerSettingsWithEndpointsForWorker($workerId)
        )->get();
    }

    public function getOneCrawlerSettingWithEndpointByEndpointId(string $endpointId): Collection {
        return $this->getBuilderBasedOnCriteria(
            new CrawlerSettingWithEndpointForLocalFetcher($endpointId)
        )->get();
    }

    public function getHasProxyImage(int $publisherId, int $channelId): bool {
        return $this->getBuilderBasedOnCriteria(
            new HasProxyImageForPublisher($publisherId, $channelId)
        )->value('has_proxy_image') === true;
    }

    public function createFromObject(EndpointObject $endpointObject): PublisherEndpoint {
        return $this->create([
            'crawler_setting_id' => $endpointObject->getCrawlerSettingId(),
            'endpoint'           => $endpointObject->getEndpoint(),
            'default_categories' => $endpointObject->getDefaultCategories(),
            'default_topics'     => $endpointObject->getDefaultTopics(),
            'has_proxy_image'    => $endpointObject->hasProxyImage(),
        ]);
    }

    public function updateFromObject(EndpointObject $endpointObject, int $endpointId): bool {
        return $this->update('id', $endpointId, [
            'crawler_setting_id' => $endpointObject->getCrawlerSettingId(),
            'endpoint'           => $endpointObject->getEndpoint(),
            'default_categories' => $endpointObject->getDefaultCategories(),
            'default_topics'     => $endpointObject->getDefaultTopics(),
            'has_proxy_image'    => $endpointObject->hasProxyImage(),
        ]);
    }
}
