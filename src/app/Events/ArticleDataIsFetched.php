<?php

declare(strict_types=1);

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class ArticleDataIsFetched implements PubSubEvent {
    use SerializesModels;

    /**
     * Emit pubsub message
     * fetcher-service-subscriber will listen to this event and calls api to parse data.
     */
    public function __construct(
        private int $publisherId,
        private int $channelId,
        private string $articleData,
        private bool $isRss,
        private string $customPrompt,
        private bool $useHeadlessBrowser,
    ) {
    }

    public function getPublisherId(): int {
        return $this->publisherId;
    }

    public function getChannelId(): int {
        return $this->channelId;
    }

    public function getArticleData(): string {
        return $this->articleData;
    }

    public function isRss(): bool {
        return $this->isRss;
    }

    public function getCustomPrompt(): string {
        return $this->customPrompt;
    }

    public function useHeadlessBrowser(): bool {
        return $this->useHeadlessBrowser;
    }

    public function getTopic(): string {
        return config('google.pubsub.topics.fetcher_actions');
    }

    public function getPayload(): array {
        return [
            'publisher_id'         => $this->publisherId,
            'channel_id'           => $this->channelId,
            'article_data'         => $this->articleData,
            'is_rss'               => $this->isRss,
            'custom_prompt'        => $this->customPrompt,
            'use_headless_browser' => $this->useHeadlessBrowser,
        ];
    }
}
