<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Logics\PassArticleDataToParserAdderServiceLogic;
use App\Modules\Fetcher\Logics\RetrieveArticleDataFromRawContentLogic;
use App\Modules\Fetcher\Logics\RetrieveEndpointForLocalFetcherLogic;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Throwable;

class FetchAndProcessArticlesByEndpointCommand extends Command {
    protected $signature = 'fetch-and-process-articles-by-endpoint {publisherEndpointId}';

    protected $description = 'Fetches content from configured endpoints according to publisher endpoint id, extracts article data, and prepares it for processing by the Parser Adder service';

    public function __construct(
        private RetrieveEndpointForLocalFetcherLogic $retrieveEndpointForLocalFetcherLogic,
        private RetrieveArticleDataFromRawContentLogic $retrieveArticleDataFromRawContentLogic,
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private PassArticleDataToParserAdderServiceLogic $passArticleDataToParserAdderServiceLogic,
        private ContentHelper $contentHelper,
    ) {
        parent::__construct();
    }

    public function handle(): void {
        $publisherEndpointId = $this->argument('publisherEndpointId');

        try {
            Log::info('Fetching articles for endpoint ' . $publisherEndpointId);
            $endpoints = $this->retrieveEndpointForLocalFetcherLogic->execute($publisherEndpointId)['data'];

            foreach ($endpoints as $endpoint) {
                try {
                    Log::info('Fetching articles for endpoint ' . $endpoint['endpoint']);
                    $rawHtmlOrRss     = $endpoint['use_headless_browser'] === true ? $this->headlessBrowserService->getRawHtmlOrRss($endpoint['endpoint'], $endpoint['custom_user_agent']) : $this->bypassCloudflareService->getRawHtmlOrRss($endpoint['endpoint'], $endpoint['custom_user_agent']);
                    $articleDataArray = $this->retrieveArticleDataFromRawContentLogic->execute($rawHtmlOrRss);

                    if ($articleDataArray === []) {
                        Log::info('No article data found for endpoint ' . $endpoint['endpoint']);

                        continue;
                    }
                    $this->passArticleDataToParserAdderServiceLogic->execute(
                        $endpoint['publisher_id'],
                        $endpoint['channel_id'],
                        $articleDataArray,
                        $this->contentHelper->isContentRss($rawHtmlOrRss->getRawContent()),
                        $endpoint['custom_prompt'],
                        $endpoint['use_headless_browser']
                    );
                    Log::info('Successfully processed endpoint ' . $endpoint['endpoint']);
                } catch (Throwable $endpointError) {
                    Log::error('Error processing endpoint', [
                        'endpoint' => $endpoint['endpoint'],
                        'error'    => $endpointError->getMessage(),
                        'trace'    => $endpointError->getTraceAsString(),
                    ]);

                    continue;
                }
            }
        } catch (Throwable $e) {
            Log::error('Error fetching articles', [
                'endpoint_id' => $publisherEndpointId,
                'error'       => $e->getMessage(),
            ]);
        }
    }
}
