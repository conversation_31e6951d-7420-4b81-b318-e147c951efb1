<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\ValueObjects\CrawlerSettingObject;
use App\Classes\ValueObjects\EndpointObject;
use App\Repositories\PublisherCrawlerSettingRepository;
use App\Repositories\PublisherEndpointRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PopulateCrawlerSettingCommand extends Command {
    protected $signature = 'populate-crawler-setting';

    protected $description = 'Adds or updates crawler setting for publisher';

    public function __construct(
        private PublisherCrawlerSettingRepository $publisherCrawlerSettingsRepository,
        private PublisherEndpointRepository $publisherEndpointsRepository,
    ) {
        parent::__construct();
    }

    public function handle(): void {
        Log::info('Starting crawler setting configuration...');
        Log::info('Please provide the basic information for the crawler setting:');
        $publisherId     = (int) $this->ask('Publisher ID');
        $channelId       = (int) $this->ask('Channel ID');
        $host            = $this->ask('Host (e.g., https://example.com)');
        $worker          = $this->ask('Worker (w1, w2, w3, w4, w5, w6)');
        $frequency       = (int) $this->ask('Frequency (minutes)', '15');
        $enabled         = $this->confirm('Enabled?', true);
        $needRandom      = $this->confirm('Need random? (optional)', true);
        $useSmartCrawler = $this->confirm('Use smart crawler?', false);
        $toPuppeteer     = $this->confirm('Use Puppeteer?', false);
        $customUserAgent = $this->ask('Custom user agent (optional)', null);
        $customPrompt    = $this->ask('Custom prompt (optional)', null);
        $partner         = (int) $this->ask('Partner (1/0)', '1');
        $type            = $this->ask('Type (optional)', 'rss');
        $customFetcher   = $this->ask('Custom fetcher (optional)', null);
        $useHeadlessBrowser = $this->confirm('Use headless browser?', false);
        $crawlType       = 'article';

        $crawlerSettingObject = new CrawlerSettingObject(
            $publisherId,
            $channelId,
            $host,
            $enabled,
            $partner,
            $needRandom,
            $frequency,
            $type,
            $crawlType,
            $customFetcher,
            $worker,
            $customUserAgent,
            $useSmartCrawler,
            $useHeadlessBrowser,
            $customPrompt,
            $toPuppeteer,
        );

        $existingCrawlerSetting = $this->publisherCrawlerSettingsRepository->findWhere(['publisher_id' => $publisherId, 'channel_id' => $channelId]);

        if ($existingCrawlerSetting !== null) {
            Log::info('Found existing crawler setting. Updating...');
            $this->publisherCrawlerSettingsRepository->updateFromObject($crawlerSettingObject, $existingCrawlerSetting->id);
            Log::info('Crawler setting updated successfully!');

            if ($this->confirm('Update/Create endpoint? (yes/no)', true) === true) {
                $this->insertEndpoint($existingCrawlerSetting->id);
            }

            return;
        }

        Log::info('Creating new crawler setting...');
        $crawlerSettingId = $this->publisherCrawlerSettingsRepository->createFromObject($crawlerSettingObject)->id;
        Log::info('New crawler setting created successfully!');
        $this->insertEndpoint($crawlerSettingId);
    }

    private function insertEndpoint(int $crawlerSettingId): void {
        Log::info('Please provide the endpoint details:');
        $endpoint          = $this->ask('Endpoint URL');
        $defaultCategories = $this->ask('Default categories (comma-separated, optional)', null);
        $defaultTopics     = $this->ask('Default topics (comma-separated, optional)', null);
        $hasProxyImage     = $this->confirm('Use proxy image?', true);

        $endpointObject = new EndpointObject(
            $crawlerSettingId,
            $endpoint,
            $defaultCategories,
            $defaultTopics,
            $hasProxyImage,
        );

        $existingEndpoint = $this->publisherEndpointsRepository->findWhere(['endpoint' => $endpoint, 'crawler_setting_id' => $crawlerSettingId]);
        if ($existingEndpoint !== null) {
            Log::info('Found existing endpoint. Updating...');
            $this->publisherEndpointsRepository->updateFromObject($endpointObject, $existingEndpoint->id);
            Log::info('Endpoint updated successfully!');

            return;
        }

        Log::info('Creating new endpoint...');
        $this->publisherEndpointsRepository->createFromObject($endpointObject);
        Log::info('New endpoint created successfully!');
    }
}
