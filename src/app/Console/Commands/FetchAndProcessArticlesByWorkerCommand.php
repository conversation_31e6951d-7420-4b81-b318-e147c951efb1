<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Logics\PassArticleDataToParserAdderServiceLogic;
use App\Modules\Fetcher\Logics\RetrieveArticleDataFromRawContentLogic;
use App\Modules\Fetcher\Logics\RetrieveEndpointsForWorkerLogic;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Throwable;

class FetchAndProcessArticlesByWorkerCommand extends Command {
    protected $signature = 'fetch-and-process-articles-by-worker {workerId}';

    protected $description = 'Fetches content from configured endpoints according to worker id, extracts article data, and prepares it for processing by the Parser Adder service';

    public function __construct(
        private RetrieveEndpointsForWorkerLogic $retrieveEndpointsForWorkerLogic,
        private RetrieveArticleDataFromRawContentLogic $retrieveArticleDataFromRawContentLogic,
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private PassArticleDataToParserAdderServiceLogic $passArticleDataToParserAdderServiceLogic,
        private ContentHelper $contentHelper,
    ) {
        parent::__construct();
    }

    public function handle(): void {
        $workerId = $this->argument('workerId');

        try {
            Log::info('Fetching articles for worker ' . $workerId);
            $endpoints = $this->retrieveEndpointsForWorkerLogic->execute($workerId)['data'];

            if ($endpoints === []) {
                Log::info('No endpoints for worker ' . $workerId . ', ending process...');

                return;
            }

            foreach ($endpoints as $endpoint) {
                try {
                    Log::info('Fetching articles for endpoint ' . $endpoint['endpoint']);
                    $rawHtmlOrRss     = $endpoint['use_headless_browser'] === true ? $this->headlessBrowserService->getRawHtmlOrRss($endpoint['endpoint'], $endpoint['custom_user_agent']) : $this->bypassCloudflareService->getRawHtmlOrRss($endpoint['endpoint'], $endpoint['custom_user_agent']);
                    $articleDataArray = $this->retrieveArticleDataFromRawContentLogic->execute($rawHtmlOrRss);

                    $this->passArticleDataToParserAdderServiceLogic->execute(
                        $endpoint['publisher_id'],
                        $endpoint['channel_id'],
                        $articleDataArray,
                        $this->contentHelper->isContentRss($rawHtmlOrRss->getRawContent()),
                        $endpoint['custom_prompt'],
                        $endpoint['use_headless_browser']
                    );
                    Log::info('Successfully processed endpoint ' . $endpoint['endpoint']);
                } catch (Throwable $endpointError) {
                    Log::error('Error processing endpoint', [
                        'endpoint' => $endpoint['endpoint'],
                        'error'    => $endpointError->getMessage(),
                        'trace'    => $endpointError->getTraceAsString(),
                    ]);

                    continue;
                }
            }
        } catch (Throwable $e) {
            Log::error('Error fetching articles', [
                'worker_id' => $workerId,
                'error'     => $e->getMessage(),
            ]);
        }
    }
}
