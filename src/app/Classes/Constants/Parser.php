<?php

declare(strict_types=1);

namespace App\Classes\Constants;

class Parser {
    public const REQUIRED_FIELDS = [
        self::TITLE,
        self::FULL_CONTENT,
        self::LINK,
    ];

    public const DEFAULT_PARSED_CONTENT_FIELDS = [
        self::ARTICLE_ID      => 0,
        self::LINK            => '',
        self::TITLE           => '',
        self::DESCRIPTION     => '',
        self::FULL_CONTENT    => '',
        self::COVER_IMAGE_URL => [
            'url'     => '',
            'caption' => null,
        ],
        self::AUTHOR         => '',
        self::PUBLISHED_DATE => 0,
        self::MODIFIED_DATE  => 0,
        self::MEDIA          => [],
    ];

    public const ARTICLE_ID = 'article_id';

    public const LINK = 'link';

    public const TITLE = 'title';

    public const DESCRIPTION = 'description';

    public const FULL_CONTENT = 'full_content';

    public const COVER_IMAGE_URL = 'cover_image_url';

    public const AUTHOR = 'author';

    public const PUBLISHED_DATE = 'published_date';

    public const MODIFIED_DATE = 'modified_date';

    public const MEDIA = 'media';

    public const CONTENT_MD5 = 'content_md5';

    public const MAX_DESCRIPTION_WORD_COUNT = 150;

    public const MAX_ADS_COUNT = 5;

    public const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    public const VIDEO_EXTENSIONS = ['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv'];

    public const CANONICAL_URL_VALUE = 'utm_source=Newswav&utm_medium=App';

    public const CHANNELS_TO_EXCLUDE_PUBLISHED_DATE = [];

    public const CHANNELS_TO_BYPASS_PREDICTION = [20];

    public const NEWSWAV_PUBLISHER_IDS = [96, 101, 369, 1062];

    public const HIDE_FOLLOW_BUTTON_PUBLISHER_IDS = [96, 101, 369, 1062];

    public const HIDE_HIDE_BUTTON_PUBLISHER_IDS = [96, 101, 369, 1062];

    public const PROJECTS_TO_SHOW = ['newswav', 'headliner', 'wavmaker', 'ugc'];
}
