<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

use App\Models\PublisherEndpoint;

class EndpointWithCrawlerSettingObject {
    public function __construct(
        private PublisherEndpoint $publisherEndpoint,
        private CrawlerSettingForEndpointObject $crawlerSettingForEndpointObject,
    ) {
    }

    public function getPublisherEndpoint(): PublisherEndpoint {
        return $this->publisherEndpoint;
    }

    public function getCrawlerSetting(): CrawlerSettingForEndpointObject {
        return $this->crawlerSettingForEndpointObject;
    }
}
