<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

class JwtUserData {
    public function __construct(
        private string $iss,
        private string $aud,
        private int $iat,
        private int $exp,
        private ?string $profileId,
        private ?int $newswavId,
        private string $sdk,
        private ?string $firebaseId,
        private ?string $userId,
        private bool $isNew,
    ) {
    }

    public static function createFromArray(array $data): self {
        return new self(
            $data['iss'], $data['aud'], $data['iat'], $data['exp'], $data['pr_id'],
            $data['nw_id'], $data['sdk'], $data['fi_id'], $data['u_id'] ?? null, $data['new'] ?? false
        );
    }

    public function toArray(): array {
        return [
            'iss'   => $this->getIss(),
            'aud'   => $this->getAud(),
            'iat'   => $this->getIat(),
            'exp'   => $this->getExp(),
            'pr_id' => $this->getProfileId(),
            'nw_id' => $this->getNewswavId(),
            'sdk'   => $this->getSdk(),
            'fi_id' => $this->getFirebaseId(),
            'u_id'  => $this->getUserId(),
            'new'   => $this->isNew(),
        ];
    }

    public function getIss(): string {
        return $this->iss;
    }

    public function getAud(): string {
        return $this->aud;
    }

    public function getIat(): int {
        return $this->iat;
    }

    public function getExp(): int {
        return $this->exp;
    }

    public function getProfileId(): ?string {
        return $this->profileId;
    }

    public function getNewswavId(): ?int {
        return $this->newswavId;
    }

    public function getSdk(): string {
        return $this->sdk;
    }

    public function getFirebaseId(): ?string {
        return $this->firebaseId;
    }

    public function getUserId(): ?string {
        return $this->userId;
    }

    public function isNew(): bool {
        return $this->isNew;
    }
}
