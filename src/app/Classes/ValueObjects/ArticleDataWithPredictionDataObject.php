<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

use App\Models\Article;
use App\Models\Channel;
use App\Models\Prediction;
use App\Models\Publisher;

class ArticleDataWithPredictionDataObject {
    public function __construct(
        private Prediction $prediction,
        private Article $article,
        private Channel $channel,
        private Publisher $publisher,
    ) {
    }

    public function getPrediction(): Prediction {
        return $this->prediction;
    }

    public function getPublisher(): Publisher {
        return $this->publisher;
    }

    public function getArticle(): Article {
        return $this->article;
    }

    public function getChannel(): Channel {
        return $this->channel;
    }
}
