<?php

declare(strict_types=1);

namespace App\Classes\StructuredData;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Enumerable;
use League\Fractal\Manager;
use League\Fractal\Pagination\IlluminatePaginatorAdapter;
use League\Fractal\Resource\Collection as FractalCollection;
use League\Fractal\Resource\Item;
use League\Fractal\TransformerAbstract;

class StructuresDataForApi {
    public const ITEMS_PER_PAGE = 10;

    public function __construct(private Manager $manager) {
    }

    public function returnOne(
        object $object,
        TransformerAbstract $transformer,
        ?string $resourceKey = null,
    ): array {
        return $this->manager->createData(new Item($object, $transformer, $resourceKey))
            ->toArray();
    }

    public function returnMany(
        Enumerable $collection,
        TransformerAbstract $transformer,
        ?string $resourceKey = null,
    ): array {
        return $this->manager->createData(new FractalCollection($collection, $transformer, $resourceKey))->toArray();
    }

    public function returnPaginated(
        LengthAwarePaginator $paginator,
        TransformerAbstract $transformer,
        ?string $resourceKey = null,
    ): array {
        $resource = new FractalCollection($paginator, $transformer, $resourceKey);
        $resource->setPaginator(new IlluminatePaginatorAdapter($paginator));

        return $this->manager->createData($resource)->toArray();
    }
}
