<?php

declare(strict_types=1);

namespace App\Classes\StructuredData\Transformers;

use App\Classes\ValueObjects\EndpointWithCrawlerSettingObject;

class EndpointsForFetcherTransformer extends BaseTransformer {
    public function transform(EndpointWithCrawlerSettingObject $object): array {
        $endpoint       = $object->getPublisherEndpoint();
        $crawlerSetting = $object->getCrawlerSetting()->getPublisherCrawlerSetting();

        return [
            'endpoint_id'          => $endpoint->id,
            'crawler_setting_id'   => $endpoint->crawler_setting_id,
            'publisher_id'         => $crawlerSetting->publisher_id,
            'channel_id'           => $crawlerSetting->channel_id,
            'host'                 => $crawlerSetting->host,
            'endpoint'             => $endpoint->endpoint,
            'frequency'            => $crawlerSetting->frequency,
            'custom_user_agent'    => $crawlerSetting->custom_user_agent,
            'custom_prompt'        => $crawlerSetting->custom_prompt,
            'use_headless_browser' => $crawlerSetting->use_headless_browser,
        ];
    }
}
