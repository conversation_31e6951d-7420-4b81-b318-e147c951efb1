<?php

declare(strict_types=1);

namespace App\Classes\StructuredData\Transformers;

use League\Fractal\Resource\Collection;
use League\Fractal\Resource\Item;
use League\Fractal\Resource\NullResource;
use League\Fractal\Resource\Primitive;
use League\Fractal\TransformerAbstract;

class BaseTransformer extends TransformerAbstract {
    public function transformAsItem(mixed $object, TransformerAbstract $transformer, ?array $defaultValue = null): mixed {
        return $object !== null ?
            new Item($object, $transformer) :
            ($defaultValue !== null ? new Primitive($defaultValue) : new NullResource());
    }

    public function transformAsCollection(mixed $object, TransformerAbstract $transformer): Collection {
        return new Collection($object, $transformer, 'COLLECTION');
    }
}
