<?php

declare(strict_types=1);

namespace App\Classes\StructuredData\Transformers;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use Carbon\Carbon;

class ArticleDataWithPredictionDataTransformer extends BaseTransformer {
    public function transform(ArticleDataWithPredictionDataObject $object): array {
        $prediction       = $object->getPrediction();
        $article          = $object->getArticle();
        $channel          = $object->getChannel();
        $publisher        = $object->getPublisher();

        return [
            'contentId'        => $article->uniqueID,
            'title'            => $article->title,
            'thumbnailUrl'     => $prediction->thumbnailURL,
            'imageAspectRatio' => $this->calculateAspectRatio($prediction->wideImageWidth, $prediction->wideImageHeight),
            'description'      => $article->description,
            'publisher'        => (object) [
                'id'                      => $publisher->id,
                'name'                    => $publisher->name,
                'url'                     => $publisher->website_url,
                'project'                 => $publisher->project,
                'enabled'                 => $publisher->enabled === 1,
                'imageUrl'                => $publisher->logo_url,
                'isVerified'              => (bool) $publisher->verified,
                'permalink'               => $publisher->permalink,
                'isNewswav'               => in_array((int) $publisher->id, Parser::NEWSWAV_PUBLISHER_IDS, true),
                'showFollowButton'        => in_array((int) $publisher->id, Parser::HIDE_FOLLOW_BUTTON_PUBLISHER_IDS, true) === false,
                'showHideButton'          => in_array((int) $publisher->isDirty, Parser::HIDE_HIDE_BUTTON_PUBLISHER_IDS, true) === false,
                'showNotInterestedButton' => in_array((int) $publisher->id, Parser::HIDE_HIDE_BUTTON_PUBLISHER_IDS, true) === false,
            ],
            'publishedAt' => Carbon::parse($article->publishedDate)->toIso8601ZuluString(),
            'language'    => strtolower($channel->language),
            'topics'      => $prediction->topic !== -1
            ? [[
                'id' => $prediction->topic,
                'en' => $prediction->topic_en,
                'ms' => $prediction->topic_ms,
                'zh' => $prediction->topic_zh,
            ]]
            : [],
            'native'              => $channel->reader_view_only === 1,
            'type'                => 'article',
            'showOriginalArticle' => in_array($publisher->project, Parser::PROJECTS_TO_SHOW, true) === false,
            'permalink'           => $article->permalink,
            'deleted'             => false,
            'originalUrl'         => $article->canonicalURL,
        ];
    }

    private function calculateAspectRatio(int $width, int $height) {
        $gcd = function ($a, $b) use (&$gcd) {
            return ($b === 0) ? $a : $gcd($b, $a % $b);
        };

        $divisor = $gcd($width, $height);

        $aspectRatioWidth  = $width / $divisor;
        $aspectRatioHeight = $height / $divisor;

        return $aspectRatioWidth . ':' . $aspectRatioHeight;
    }
}
