<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\APIException;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class NewswavOutboxClient {
    public function __construct(private Client $client) {
    }

    public function emitMessage(
        string $topic,
        array $payload,
        string $keyField,
        string $eventName,
    ): void {
        $payload['host'] = ['hostname' => gethostname()];

        $this->makeRequest('', 'POST', [
            'topic'        => $topic,
            'payload'      => [
                'eventName' => $eventName,
                'payload'   => $payload,
            ],
            'service_name' => gethostname(),
            'key'          => $this->extractKey($keyField, $payload),
        ]);
    }

    /**
     * @param array<string,mixed> $parameters
     *
     * @throws APIException
     */
    private function makeRequest(string $endpoint, string $method, array $parameters): ResponseInterface {
        try {
            $response = $this->client->request($method, config('newswav_services.outbox_service.base_url') . $endpoint, [
                'headers' => [
                    'Content-Type'                                       => 'application/json',
                    config('newswav_services.outbox_service.header_key') => config('newswav_services.outbox_service.api_key'),
                ],
                'json' => $parameters,
            ]);

            return $response;
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }

    private function extractKey(string $keyPath, array $payload): ?string {
        try {
            $keys  = explode('.', $keyPath);
            $value = $payload;
            foreach ($keys as $key) {
                if (is_array($value) && isset($value[$key])) {
                    $value = $value[$key];
                } else {
                    return null;
                }
            }

            if (is_scalar($value) || $value === null) {
                return (string) $value;
            }

            return substr(json_encode($value), 0, 100);
        } catch (Exception $e) {
            Log::warning("Failed to extract key from payload: {$keyPath}");

            return null;
        }
    }
}
