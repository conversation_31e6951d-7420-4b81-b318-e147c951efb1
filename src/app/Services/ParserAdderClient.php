<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\APIException;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;

class ParserAdderClient {
    public function __construct(private Client $client) {
    }

    /**
     * @throws APIException
     */
    public function postArticleDataToParserAdderService(int $publisherId, int $channelId, string $articleData, bool $isRss, ?string $customPrompt, bool $useHeadlessBrowser): void {
        try {
            $this->makeRequest(
                'parse',
                ServerParameters::HTTP_METHOD_POST,
                [
                    'publisher_id'         => $publisherId,
                    'channel_id'           => $channelId,
                    'article_data'         => $articleData,
                    'is_rss'               => $isRss,
                    'custom_prompt'        => $customPrompt,
                    'use_headless_browser' => $useHeadlessBrowser,
                ]
            );
        } catch (Exception $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * @param array<string,mixed> $parameters
     *
     * @throws APIException
     */
    private function makeRequest(string $endpoint, string $method, array $parameters): ResponseInterface {
        try {
            $baseUrl  = rtrim(config('newswav_services.parser_adder_service.base_url'), '/');
            $url      = $baseUrl . '/' . ltrim($endpoint, '/');
            $response =  $this->client->request($method, $url, [
                'headers' => [
                    'Content-Type'  => 'application/json',
                ],
                'json' => $parameters,
            ]);

            return $response;
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }
}
