<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Classes\Constants\ServerParameters;
use App\Http\Middleware\Authenticate;
use Faker\Factory;
use Illuminate\Http\Request;

trait TestRequestGenerators {
    protected function createRequest(array $parameters = [], array $files = [], string $method = ServerParameters::HTTP_METHOD_GET): Request {
        return $this->createRequestFromParameters($method, $parameters, $files);
    }

    protected function createRequestAsClient(Request $request, ?string $nwToken = null): Request {
        $generator = Factory::create();
        $nwToken ??= $generator->uuid;
        $request->headers->add([
            Authenticate::TOKEN_HEADER_KEY => $nwToken,
        ]);

        return $request;
    }

    private function createRequestFromParameters(string $method, array $parameters, array $files): Request {
        $request = new Request();

        if ($method === ServerParameters::HTTP_METHOD_GET) {
            $request->initialize($parameters);
        }

        if ($method !== ServerParameters::HTTP_METHOD_GET) {
            $request->initialize([], $parameters, [], [], $files);
        }

        $request->setMethod($method);

        return $request;
    }
}
