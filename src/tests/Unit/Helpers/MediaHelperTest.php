<?php

declare(strict_types=1);

namespace Tests\Unit\Helpers;

use App\Helpers\MediaHelper;
use PHPUnit\Framework\TestCase;

class MediaHelperTest extends TestCase {
    public function testItIsImageUrl(): void {
        $mediaHelper = new MediaHelper();

        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.jpg'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.jpeg'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.png'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.gif'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.webp'));

        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.mp4'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.mov'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.avi'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.wmv'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.flv'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.mkv'));
    }

    public function testItIsVideoUrl(): void {
        $mediaHelper = new MediaHelper();

        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.mp4'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.mov'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.avi'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.wmv'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.flv'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.mkv'));

        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.jpg'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.jpeg'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.png'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.gif'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.webp'));
    }

    public function testItGetImageSize(): void {
        $mediaHelper = new MediaHelper();

        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.jpg'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.jpeg'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.png'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.webp'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.gif'));
    }

    public function testItIsMediaUrlValid(): void {
        $mediaHelper = new MediaHelper();

        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.jpg'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.jpeg'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.png'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.webp'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.gif'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://sample-videos.com/video321/mp4/720/big_buck_bunny_720p_1mb.mp4'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://samples.mplayerhq.hu/avi/AV36_1.AVI'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://samples.mplayerhq.hu/FLV/11-04-2008.flv'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://sample-videos.com/video321/mkv/720/big_buck_bunny_720p_1mb.mkv'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://samples.mplayerhq.hu/mov/RQ004F14.MOV'));
    }
}
