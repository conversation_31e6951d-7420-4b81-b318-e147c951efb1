<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Exceptions\APIException;
use App\Services\NewswavOutboxClient;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class NewswavOutboxClientTest extends TestCase {
    public function testItEmitsMessage(): void {
        $topic      = 'test-topic';
        $payload    = ['test' => 'value'];
        $keyField   = 'test';
        $eventName  = 'test-event';
        $clientMock = Mockery::mock(Client::class);

        $fakeResponse = new Response(200, [], '');
        $clientMock->shouldReceive('request')
            ->once()
            ->with(
                'POST',
                Mockery::any(),
                Mockery::on(function ($actualRequest) use ($topic, $eventName) {
                    $json = $actualRequest['json'];

                    return $json['topic'] === $topic
                           && $json['payload']['eventName'] === $eventName
                           && $json['payload']['payload']['test'] === 'value'
                           && isset($json['payload']['payload']['host']['hostname'])
                           && $json['service_name'] === gethostname()
                           && $json['key'] === 'value';
                }))
            ->andReturn($fakeResponse);

        $service = new NewswavOutboxClient($clientMock);
        $service->emitMessage($topic, $payload, $keyField, $eventName);
        $this->assertTrue(true);
    }

    public function testItThrowsExceptionAtGuzzleException(): void {
        $this->expectException(APIException::class);
        $topic      = 'test-topic';
        $payload    = ['test' => 'value'];
        $keyField   = 'test';
        $eventName  = 'test-event';
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new class extends Exception implements GuzzleException {
            });

        $service = new NewswavOutboxClient($clientMock);
        $service->emitMessage($topic, $payload, $keyField, $eventName);
    }
}
