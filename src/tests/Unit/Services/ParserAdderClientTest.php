<?php

declare(strict_types=1);

namespace Src\Tests\Unit\Services;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\APIException;
use App\Services\ParserAdderClient;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class ParserAdderClientTest extends TestCase {
    public function testItPostsArticleDataToParserAdderService(): void {

        $publisherId        = $this->generator->numberBetween(1, 1000);
        $channelId          = $this->generator->numberBetween(1, 1000);
        $articleData        = $this->generator->sentence();
        $isRss              = $this->generator->boolean();
        $customPrompt       = $this->generator->sentence();
        $useHeadlessBrowser = $this->generator->boolean();

        $fakeResponse = new Response(200, [], '');
        $clientMock   = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->with(
                ServerParameters::HTTP_METHOD_POST,
                Mockery::type('string'),
                [
                    'headers' => [
                        'Content-Type'  => 'application/json',
                    ],
                    'json' => [
                        'publisher_id'         => $publisherId,
                        'channel_id'           => $channelId,
                        'article_data'         => $articleData,
                        'is_rss'               => $isRss,
                        'custom_prompt'        => $customPrompt,
                        'use_headless_browser' => $useHeadlessBrowser,
                    ],
                ]
            )
            ->andReturn($fakeResponse);

        $service = new ParserAdderClient($clientMock);
        $service->postArticleDataToParserAdderService($publisherId, $channelId, $articleData, $isRss, $customPrompt, $useHeadlessBrowser);
        $this->assertTrue(true);
    }

    public function testItThrowsExceptionAtGuzzleException(): void {
        $this->expectException(APIException::class);
        $publisherId        = $this->generator->numberBetween(1, 1000);
        $channelId          = $this->generator->numberBetween(1, 1000);
        $articleData        = $this->generator->sentence();
        $isRss              = $this->generator->boolean();
        $customPrompt       = $this->generator->sentence();
        $useHeadlessBrowser = $this->generator->boolean();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new Exception());

        $service = new ParserAdderClient($clientMock);
        $service->postArticleDataToParserAdderService($publisherId, $channelId, $articleData, $isRss, $customPrompt, $useHeadlessBrowser);
    }
}
