<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Middleware;

use App\Classes\ValueObjects\JwtUserData;
use App\Exceptions\UnauthorisedException;
use App\Http\Middleware\Authenticate;
use App\Modules\Authentication\Services\JwtManager;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;
use Tests\Traits\TestTraits;

class AuthenticateTest extends TestCase {
    use TestTraits;

    public function testItHandle(): void {
        $jwtManager = Mockery::mock(JwtManager::class);
        $jwtManager->shouldReceive('decode')->andReturn(new JwtUserData('iss', 'aud', 1, 2, 'pr_id', 1, 'sdk', 'fi_id', 'u_id', false));

        $authenticate = new Authenticate($jwtManager);

        $request = $this->createRequest();
        $request = $this->createRequestAsClient($request);

        $response = $authenticate->handle($request, function ($request) {
            return new JsonResponse(['message' => 'Hello World']);
        });

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function testItHandleWithoutToken(): void {
        $jwtManager = Mockery::mock(JwtManager::class);

        $authenticate = new Authenticate($jwtManager);

        $request = $this->createRequest();

        $this->expectException(UnauthorisedException::class);

        $authenticate->handle($request, function ($request) {
            return new JsonResponse(['message' => 'Hello World']);
        });
    }

    public function testItHandleWithInvalidToken(): void {
        $jwtManager = Mockery::mock(JwtManager::class);
        $jwtManager->shouldReceive('decode')->andThrow(new Exception());

        $authenticate = new Authenticate($jwtManager);

        $request = $this->createRequest();
        $request = $this->createRequestAsClient($request);

        $this->expectException(UnauthorisedException::class);

        $authenticate->handle($request, function ($request) {
            return new JsonResponse([]);
        });
    }
}
