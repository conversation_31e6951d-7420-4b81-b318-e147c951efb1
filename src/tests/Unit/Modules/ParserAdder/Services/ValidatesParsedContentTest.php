<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Helpers\MediaHelper;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use App\Repositories\PublisherRepository;
use Mockery;
use RuntimeException;
use Tests\TestCase;

class ValidatesParsedContentTest extends TestCase {
    public function testItValidatesParsedContent(): void {

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($parsedContent, $validatedContent);
    }

    public function testItThrowsRuntimeExceptionOnFailure(): void {
        $this->expectException(RuntimeException::class);

        $parsedContent           = [];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $service->execute($parsedContent, $mockPublisher->id);
    }
}
