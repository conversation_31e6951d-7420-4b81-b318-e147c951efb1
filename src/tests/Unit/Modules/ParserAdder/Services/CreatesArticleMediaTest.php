<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Helpers\MediaHelper;
use App\Modules\ParserAdder\Services\CreatesArticleMedia;
use App\Modules\ParserAdder\Services\RetrievesImageProxyLinks;
use App\Repositories\ArticleRepository;
use App\Repositories\MediaRepository;
use App\Repositories\PublisherEndpointRepository;
use App\Repositories\ThumbnailRepository;
use Mockery;
use Tests\TestCase;

class CreatesArticleMediaTest extends TestCase {
    public function testItItCreatesArticleWithMedias(): void {
        $publisherId    = 123;
        $channelId      = 456;
        $mockCoverImage = [
            'url'     => $this->generator->url(),
            'caption' => $this->generator->sentence(),
        ];
        $mockMediaArray = [
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
        ];

        $mockMedia1     = $this->createMedia(['id' => 1, 'url' => $mockCoverImage['url']]);
        $mockMedia2     = $this->createMedia(['id' => 2, 'url' => $mockMediaArray[0]['url']]);
        $mockMedia3     = $this->createMedia(['id' => 3, 'url' => $mockMediaArray[1]['url']]);
        $mockThumbnail1 = $this->createThumbnail(1);
        $mockThumbnail2 = $this->createThumbnail(2);
        $mockThumbnail3 = $this->createThumbnail(3);

        $articleRepositoryMock           = Mockery::mock(ArticleRepository::class);
        $mediaRepositoryMock             = Mockery::mock(MediaRepository::class);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $thumbnailRepositoryMock         = Mockery::mock(ThumbnailRepository::class);
        $retrievesImageProxyLinksMock    = Mockery::mock(RetrievesImageProxyLinks::class);
        $mediaHelperMock                 = Mockery::mock(MediaHelper::class);

        $publisherEndpointRepositoryMock
            ->shouldReceive('getHasProxyImage')
            ->once()
            ->with($publisherId, $channelId)
            ->andReturn(false);

        $mediaRepositoryMock->shouldReceive('findWhere')
            ->times(3)
            ->andReturn(null);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->times(9)
            ->andReturn(true);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->times(3)
            ->andReturn(false);

        $mediaHelperMock->shouldReceive('getImageSize')
            ->times(3)
            ->andReturn(['width' => 800, 'height' => 600]);

        $mediaRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockMedia1, $mockMedia2, $mockMedia3);

        $mediaRepositoryMock->shouldReceive('find')
            ->times(3)
            ->andReturn($mockMedia1, $mockMedia2, $mockMedia3);

        $thumbnailRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockThumbnail1, $mockThumbnail2, $mockThumbnail3);

        $service = new CreatesArticleMedia(
            $articleRepositoryMock,
            $mediaRepositoryMock,
            $publisherEndpointRepositoryMock,
            $thumbnailRepositoryMock,
            $retrievesImageProxyLinksMock,
            $mediaHelperMock
        );

        $mediaIds = $service->execute($mockCoverImage, $mockMediaArray, $publisherId, $channelId);

        $this->assertEquals([1, 2, 3], $mediaIds);
    }
}
