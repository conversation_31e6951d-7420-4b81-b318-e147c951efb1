<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Modules\ParserAdder\Services\DecoratesArticleDataWithPredictionData;
use Tests\TestCase;

class DecoratesArticleDataWithPredictionDataTest extends TestCase {
    public function testItDecoratesArticleDataWithPredictionData(): void {
        $publisher      = $this->createPublisher();
        $channel        = $this->createChannel(['publisher_id' => $publisher->id]);
        $article        = $this->createArticle(['channelID' => $channel->id]);
        $predictionData = $this->createPrediction(['unique_id' => $article->uniqueID, 'publisher_id' => $publisher->id]);

        /** @var DecoratesArticleDataWithPredictionData $service */
        $service = app(DecoratesArticleDataWithPredictionData::class);
        $result  = $service->execute($predictionData);

        $this->assertEquals($predictionData->unique_id, $result->first()->getPrediction()->unique_id);
        $this->assertEquals($article->id, $result->first()->getArticle()->id);
        $this->assertEquals($channel->id, $result->first()->getChannel()->id);
        $this->assertEquals($publisher->id, $result->first()->getPublisher()->id);
    }
}
