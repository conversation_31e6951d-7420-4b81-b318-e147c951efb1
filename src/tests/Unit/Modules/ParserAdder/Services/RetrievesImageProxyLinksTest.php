<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Modules\ParserAdder\Services\RetrievesImageProxyLinks;
use Tests\TestCase;

class RetrievesImageProxyLinksTest extends TestCase {
    public function testItRetrievesImageProxyLinkForThumbnail(): void {
        $isThumbnail = true;
        $url         = $this->generator->url();

        $service = new RetrievesImageProxyLinks();
        $links   = $service->execute($isThumbnail, $url);

        $this->assertArrayHasKey('square', $links);
        $this->assertArrayHasKey('wide', $links);
        $this->assertArrayHasKey('regular', $links);
    }

    public function testItRetrievesImageProxyLinkForRegularImage(): void {
        $isThumbnail = false;
        $url         = $this->generator->url();

        $service = new RetrievesImageProxyLinks();
        $links   = $service->execute($isThumbnail, $url);

        $this->assertArrayHasKey('regular', $links);
    }
}
