<?php

declare(strict_types=1);

// declare(strict_types=1);

// namespace Tests\Unit\Modules\ParserAdder\Logics;

// use App\Classes\StructuredData\StructuresDataForApi;
// use App\Classes\ValueObjects\ParsedArticleObject;
// use App\Helpers\ContentHelper;
// use App\Modules\ParserAdder\Logics\CreateArticleLogic;
// use App\Modules\ParserAdder\Services\CreatesArticle;
// use App\Modules\ParserAdder\Services\CreatesArticleMedia;
// use App\Modules\ParserAdder\Services\CreatesPrediction;
// use App\Modules\ParserAdder\Services\DecoratesArticleDataWithPredictionData;
// use App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle;
// use App\Modules\ParserAdder\Services\PopulatesArticleWordCount;
// use App\Modules\ParserAdder\Services\UpdatesArticle;
// use App\Repositories\ArticleRepository;
// use App\Repositories\PredictionsRepository;
// use App\Services\NewswavOutboxClient;
// use Mockery;
// use Tests\TestCase;

// class CreateArticleLogicTest extends TestCase {
//     public function testItCreateArticle(): void {
//         $publisherId  = 10;
//         $channelId    = 20;
//         $title        = $this->generator->title();
//         $description  = $this->generator->sentence();
//         $fullContent  = $this->generator->paragraph();
//         $author       = $this->generator->name();
//         $modifiedDate = $this->generator->date();
//         $canonicalURL = $this->generator->url();
//         $coverImage   = $this->generator->url();
//         $media        = $this->generator->url();
//         $contentMd5   = $this->generator->md5();

//         $parsedArticle = Mockery::mock(ParsedArticleObject::class);
//         $parsedArticle->shouldReceive('getArticleId')->andReturn($this->generator->numberBetween(1, 1000));
//         $parsedArticle->shouldReceive('getCoverImage')->andReturn(['url' => $coverImage]
//         );
//         $parsedArticle->shouldReceive('getMedia')->andReturn([$media]);
//         $parsedArticle->shouldReceive('getFullContent')->andReturn($fullContent);
//         $parsedArticle->shouldReceive('getPublishedDate')->andReturn(null);
//         $parsedArticle->shouldReceive('getTitle')->andReturn($title);
//         $parsedArticle->shouldReceive('getDescription')->andReturn($description);
//         $parsedArticle->shouldReceive('getAuthor')->andReturn($author);
//         $parsedArticle->shouldReceive('getModifiedDate')->andReturn($modifiedDate);
//         $parsedArticle->shouldReceive('getCanonicalURL')->andReturn($canonicalURL);
//         $parsedArticle->shouldReceive('getContentMd5')->andReturn($contentMd5);

//         $generatesUniqueIdForArticleMock            = Mockery::mock(GeneratesUniqueIdForArticle::class);
//         $createsArticleMediaMock                    = Mockery::mock(CreatesArticleMedia::class);
//         $populatesArticleWordCountMock              = Mockery::mock(PopulatesArticleWordCount::class);
//         $createsArticleMock                         = Mockery::mock(CreatesArticle::class);
//         $updatesArticleMock                         = Mockery::mock(UpdatesArticle::class);
//         $createsPredictionMock                      = Mockery::mock(CreatesPrediction::class);
//         $articleRepositoryMock                      = Mockery::mock(ArticleRepository::class);
//         $predictionRepositoryMock                   = Mockery::mock(PredictionsRepository::class);
//         $decoratesArticleDataWithPredictionDataMock = Mockery::mock(DecoratesArticleDataWithPredictionData::class);
//         $structureMock                              = Mockery::mock(StructuresDataForApi::class);
//         $newswavOutboxClientMock                    = Mockery::mock(NewswavOutboxClient::class);
//         $contentHelperMock                          = Mockery::mock(ContentHelper::class);

//         $logic = new CreateArticleLogic(
//             $generatesUniqueIdForArticleMock,
//             $createsArticleMediaMock,
//             $populatesArticleWordCountMock,
//             $createsArticleMock,
//             $updatesArticleMock,
//             $createsPredictionMock,
//             $articleRepositoryMock,
//             $predictionRepositoryMock,
//             $newswavOutboxClientMock,
//             $decoratesArticleDataWithPredictionDataMock,
//             $structureMock,
//             $contentHelperMock
//         );

//         $logic->execute($parsedArticle, $publisherId, $channelId);
//         $this->assertTrue(true);
//     }
// }
