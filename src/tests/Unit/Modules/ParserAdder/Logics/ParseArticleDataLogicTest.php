<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Logics;

use App\Modules\AiModels\AiModelClient;
use App\Modules\ParserAdder\Logics\ParseArticleDataLogic;
use App\Modules\ParserAdder\Services\SanitizesParsedContent;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class ParseArticleDataLogicTest extends TestCase {
    public function testItParseArticleDataFromRss(): void {

        $mockRequest = Mockery::mock(Request::class);
        $mockRequest->shouldReceive('get')->with('publisher_id')->andReturn(1);
        $mockRequest->shouldReceive('get')->with('article_data')->andReturn('<raw html content>');
        $mockRequest->shouldReceive('get')->with('is_rss')->andReturn(true);
        $mockRequest->shouldReceive('get')->with('custom_prompt', '')->andReturn('');
        $mockRequest->shouldReceive('get')->with('use_headless_browser', false)->andReturn(false);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
        ];

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn(json_encode($mockData));

        $sanitizedData                = $mockData;
        $sanitizedData['content_md5'] = md5('<raw html content>');

        $mockValidator = Mockery::mock(ValidatesParsedContent::class);
        $mockValidator->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $mockSanitizer = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizer->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $mockValidator,
            $mockSanitizer,
            $mockAiModelClient
        );
        $result = $logic->execute($mockRequest);
        $this->assertEquals($sanitizedData, $result);
    }
}
