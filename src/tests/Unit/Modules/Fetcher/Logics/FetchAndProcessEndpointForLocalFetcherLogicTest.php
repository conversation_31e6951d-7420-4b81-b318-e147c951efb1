<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Logics;

use App\Classes\ValueObjects\EndpointWithCrawlerSettingObject;
use App\Classes\ValueObjects\RawContentObject;
use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class FetchAndProcessEndpointForLocalFetcherLogicTest extends TestCase{

    public function testItFetchAndProcessEndpointWithEndpointIdUsingHeadlessBrowser(): void {
        $endpointId   = (string) $this->generator->randomNumber();
        $fakeEndpoint = $this->generator->url();
        $fakeArticleUrl = [
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSettings(1, [
            'use_headless_browser' => true,
        ])->first();
        
        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getOneCrawlerSettingWithEndpointByEndpointId')
            ->with($endpointId)
            ->once()
            ->andReturn($mockEndpointWithCrawlerSettings);

        $mockEndpointWithCrawlerSettingObject =  Mockery::mock(EndpointWithCrawlerSettingObject::class);
        $mockEndpointWithCrawlerSettingObject->shouldReceive('getPublisherId')->andReturn(1);
        $mockEndpointWithCrawlerSettingObject->shouldReceive('getChannelId')->andReturn(1);
        $mockEndpointWithCrawlerSettingObject->shouldReceive('getEndpoint')->andReturn($fakeEndpoint);
        $mockEndpointWithCrawlerSettingObject->shouldReceive('getFrequency')->andReturn(10);
        $mockEndpointWithCrawlerSettingObject->shouldReceive('useHeadlessBrowser')->andReturn(true);
        $mockEndpointWithCrawlerSettingObject->shouldReceive('getCustomPrompt')->andReturn(null);
        $mockEndpointWithCrawlerSettingObject->shouldReceive('getCustomUserAgent')->andReturn(null);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->once()
            ->andReturn(false);

        $mockHeadlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $mockHeadlessBrowserService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->once()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->once()
            ->with([])
            ->andReturn(true);

        

        $service = new FetchAndProcessEndpointForLocalFetcherLogic(
            $mockRetrievesArticleDataFromRawContentService,
            Mockery::mock(BypassCloudflareService::class),
            $mockHeadlessBrowserService,
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($endpointId);
    }
}
