<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Logics;

use App\Classes\ValueObjects\EndpointWithCrawlerSettingObject;
use App\Classes\ValueObjects\RawContentObject;
use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Mockery;
use Tests\TestCase;

class FetchAndProcessEndpointForLocalFetcherLogicTest extends TestCase {
    public function testItFetchAndProcessEndpointWithEndpointIdUsingHeadlessBrowser(): void {
        $endpointId     = (string) $this->generator->randomNumber();
        $fakeArticleUrl = [
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSetting([
            'use_headless_browser' => 1,
        ])->first();

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getOneCrawlerSettingWithEndpointByEndpointId')
            ->with($endpointId)
            ->once()
            ->andReturn($mockEndpointWithCrawlerSettings);

        $mockEndpointWithCrawlerSettingObject =  new EndpointWithCrawlerSettingObject($mockEndpointWithCrawlerSettings);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->once()
            ->andReturn(false);

        $mockHeadlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $mockHeadlessBrowserService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with($mockEndpointWithCrawlerSettingObject->getEndpoint(), $mockEndpointWithCrawlerSettingObject->getCustomUserAgent())
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->once()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->once()
            ->andReturn(true);

        $service = new FetchAndProcessEndpointForLocalFetcherLogic(
            $mockRetrievesArticleDataFromRawContentService,
            Mockery::mock(BypassCloudflareService::class),
            $mockHeadlessBrowserService,
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($endpointId);
    }

    public function testItFetchAndProcessEndpointWithEndpointIdUsingBypassCloudflare(): void {
        $endpointId     = (string) $this->generator->randomNumber();
        $fakeArticleUrl = [
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSettings(1, [
            'use_headless_browser' => false
        ])->first();

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getOneCrawlerSettingWithEndpointByEndpointId')
            ->with($endpointId)
            ->once()
            ->andReturn($mockEndpointWithCrawlerSettings);

        $mockEndpointWithCrawlerSettingObject =  new EndpointWithCrawlerSettingObject($mockEndpointWithCrawlerSettings);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->once()
            ->andReturn(false);

        $mockBypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $mockBypassCloudflareService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with($mockEndpointWithCrawlerSettingObject->getEndpoint(), $mockEndpointWithCrawlerSettingObject->getCustomUserAgent())
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->once()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->once()
            ->andReturn(true);

        $service = new FetchAndProcessEndpointForLocalFetcherLogic(
            $mockRetrievesArticleDataFromRawContentService,
            $mockBypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($endpointId);
    }
}
