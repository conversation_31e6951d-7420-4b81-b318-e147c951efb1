<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Logics;

use App\Classes\StructuredData\StructuresDataForApi;
use App\Modules\Fetcher\Logics\RetrieveEndpointForLocalFetcherLogic;
use App\Modules\Fetcher\Services\DecoratesEndpointsWithCrawlerSettingsData;
use App\Repositories\PublisherCrawlerSettingRepository;
use App\Repositories\PublisherEndpointRepository;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Tests\TestCase;

class RetrieveEndpointForLocalFetcherLogicTest extends TestCase {
    public function testItRetrievesEndpointForLocalFetcher(): void {

        $endpointId   = (string) $this->generator->randomNumber();
        $mockEndpoint = new Collection([]);

        $mockEndpointRepository       = Mockery::mock(PublisherEndpointRepository::class);
        $mockCrawlerSettingRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockEndpointRepository->shouldReceive('getOneCrawlerSettingWithEndpointByEndpointId')
            ->once()
            ->with($endpointId)
            ->andReturn($mockEndpoint);

        $mockDecoratedEndpoints = Mockery::mock(DecoratesEndpointsWithCrawlerSettingsData::class);
        $mockDecoratedEndpoints->shouldReceive('execute')
            ->once()
            ->with(Mockery::type(Collection::class))
            ->andReturn(new Collection());

        $mockStructure = Mockery::mock(StructuresDataForApi::class);
        $mockStructure->shouldReceive('returnMany')
            ->once()
            ->andReturn([]);

        $service = new RetrieveEndpointForLocalFetcherLogic(
            $mockCrawlerSettingRepository,
            $mockStructure,
            $mockDecoratedEndpoints,
            $mockEndpointRepository
        );

        $response = $service->execute($endpointId);
        static::assertSame([], $response);
    }
}
