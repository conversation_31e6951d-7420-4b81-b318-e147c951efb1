<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Logics;

use App\Classes\StructuredData\StructuresDataForApi;
use App\Modules\Fetcher\Logics\RetrieveEndpointsForWorkerLogic;
use App\Modules\Fetcher\Services\DecoratesEndpointsWithCrawlerSettingsData;
use App\Repositories\PublisherCrawlerSettingRepository;
use App\Repositories\PublisherEndpointRepository;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Tests\TestCase;

class RetrieveEndpointsForWorkerLogicTest extends TestCase {
    public function testItRetrievesEndpointForWorker(): void {

        $workerId     = (string) $this->generator->randomNumber();
        $mockEndpoint = new Collection([]);

        $mockEndpointRepository       = Mockery::mock(PublisherEndpointRepository::class);
        $mockCrawlerSettingRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpoints')
            ->once()
            ->with($workerId)
            ->andReturn($mockEndpoint);

        $mockDecoratedEndpoints = Mockery::mock(DecoratesEndpointsWithCrawlerSettingsData::class);
        $mockDecoratedEndpoints->shouldReceive('execute')
            ->once()
            ->with(Mockery::type(Collection::class))
            ->andReturn(new Collection());

        $mockStructure = Mockery::mock(StructuresDataForApi::class);
        $mockStructure->shouldReceive('returnMany')
            ->once()
            ->andReturn([]);

        $service = new RetrieveEndpointsForWorkerLogic(
            $mockCrawlerSettingRepository,
            $mockStructure,
            $mockDecoratedEndpoints,
            $mockEndpointRepository
        );

        $response = $service->execute($workerId);
        static::assertSame([], $response);
    }
}
