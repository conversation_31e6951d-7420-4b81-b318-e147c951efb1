<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Services;

use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\ExtractsRssItemsFromRawContent;
use App\Repositories\ArticleRepository;
use Mockery;
use Tests\TestCase;

class ExtractsRssItemsFromRawContentTest extends TestCase {
    public function testItExtractRssItemsFromRawContent(): void {
        $xml               = '<rss><channel><item><title>Test</title><link>http://test.com</link></item></channel></rss>';
        $mockResult        = '<item><title>Test</title><link>http://test.com</link></item>';
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->once()
            ->andReturn('http://test.com');
        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')
            ->once()
            ->andReturn(null);

        $service = new ExtractsRssItemsFromRawContent($articleRepositoryMock, $contentHelperMock);
        $result  = $service->execute($xml);
        static::assertCount(1, $result);
        static::assertEquals($mockResult, $result[0]);
    }
}
