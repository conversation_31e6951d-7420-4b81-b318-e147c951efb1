<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Services;

use App\Modules\Fetcher\Services\DecoratesEndpointsWithCrawlerSettingsData;
use Tests\TestCase;

class DecoratesEndpointsWithCrawlerSettingsDataTest extends TestCase {
    public function testItDecorateSingleEndpoint(): void {
        $crawlerSetting = $this->createPublisherCrawlerSetting();
        $endpoints      = $this->createPublisherEndpoints(1, $crawlerSetting->id);
        $endpoint       = $endpoints->first();

        /** @var DecoratesEndpointsWithCrawlerSettingsData $service */
        $service = app(DecoratesEndpointsWithCrawlerSettingsData::class);
        $result  = $service->execute($endpoint);

        static::assertCount($endpoints->count(), $result);

    }

    public function testItDecorateMultipleEndpoints(): void {
        $crawlerSetting = $this->createPublisherCrawlerSetting();
        $endpoints      = $this->createPublisherEndpoints(3, $crawlerSetting->id);

        /** @var DecoratesEndpointsWithCrawlerSettingsData $service */
        $service = app(DecoratesEndpointsWithCrawlerSettingsData::class);
        $result  = $service->execute($endpoints);

        static::assertCount($endpoints->count(), $result);
    }
}
