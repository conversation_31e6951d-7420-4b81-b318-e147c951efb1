<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750325458">
  <project timestamp="1750325458">
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Constants/AiPrompts.php">
      <class name="App\Classes\Constants\AiPrompts" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="113" ncloc="113" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Constants/Parser.php">
      <class name="App\Classes\Constants\Parser" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="72" ncloc="72" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Constants/ServerParameters.php">
      <class name="App\Classes\Constants\ServerParameters" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="32" ncloc="32" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Interfaces/AiModel.php">
      <metrics loc="16" ncloc="16" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Services/Criteria/ArticleContentWithPredictionData.php">
      <class name="App\Classes\Services\Criteria\ArticleContentWithPredictionData" namespace="global">
        <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="41" coveredstatements="0" elements="44" coveredelements="0"/>
      </class>
      <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="15" type="method" name="apply" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="57" type="method" name="getApplicableTo" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <metrics loc="61" ncloc="61" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="41" coveredstatements="0" elements="44" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Services/Criteria/CrawlerSettingWithEndpointForLocalFetcher.php">
      <class name="App\Classes\Services\Criteria\CrawlerSettingWithEndpointForLocalFetcher" namespace="global">
        <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="0" elements="18" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="16" type="method" name="apply" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="32" type="method" name="getApplicableTo" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <metrics loc="36" ncloc="36" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="0" elements="18" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Services/Criteria/CrawlerSettingsWithEndpointsForWorker.php">
      <class name="App\Classes\Services\Criteria\CrawlerSettingsWithEndpointsForWorker" namespace="global">
        <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="0" elements="18" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="16" type="method" name="apply" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="32" type="method" name="getApplicableTo" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <metrics loc="36" ncloc="36" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="0" elements="18" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Services/Criteria/Criteriable.php">
      <metrics loc="20" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/Services/Criteria/HasProxyImageForPublisher.php">
      <class name="App\Classes\Services\Criteria\HasProxyImageForPublisher" namespace="global">
        <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="14" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="17" type="method" name="apply" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="29" type="method" name="getApplicableTo" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <metrics loc="33" ncloc="33" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="14" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/ArticleDataWithPredictionDataObject.php">
      <class name="App\Classes\ValueObjects\ArticleDataWithPredictionDataObject" namespace="global">
        <metrics complexity="5" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="10" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="18" type="method" name="getPrediction" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="22" type="method" name="getArticle" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="26" type="method" name="getChannel" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="30" type="method" name="getPublisher" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <metrics loc="34" ncloc="34" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="10" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/ArticleObject.php">
      <class name="App\Classes\ValueObjects\ArticleObject" namespace="global">
        <metrics complexity="17" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="34" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="27" type="method" name="getArticleId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="31" type="method" name="getUniqueId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="35" type="method" name="getChannelId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="39" type="method" name="getTitle" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="43" type="method" name="getDescription" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="47" type="method" name="getHtml" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="51" type="method" name="getAuthor" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="55" type="method" name="getPublishedDate" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="59" type="method" name="getModifiedDate" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="63" type="method" name="getUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="67" type="method" name="getCanonicalURL" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="68" type="stmt" count="0"/>
      <line num="71" type="method" name="getMedia" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="72" type="stmt" count="0"/>
      <line num="75" type="method" name="getPermalink" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="76" type="stmt" count="0"/>
      <line num="79" type="method" name="getContentMd5" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="80" type="stmt" count="0"/>
      <line num="83" type="method" name="getRelatedArticle" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="84" type="stmt" count="0"/>
      <line num="87" type="method" name="setRelatedArticle" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="88" type="stmt" count="0"/>
      <metrics loc="91" ncloc="91" classes="1" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="34" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/CrawlerSettingObject.php">
      <class name="App\Classes\ValueObjects\CrawlerSettingObject" namespace="global">
        <metrics complexity="17" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="34" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="28" type="method" name="getPublisherId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="32" type="method" name="getChannelId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="36" type="method" name="getHost" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="40" type="method" name="isEnabled" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="44" type="method" name="isPartner" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="48" type="method" name="needRandom" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="52" type="method" name="getFrequency" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="56" type="method" name="getType" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="60" type="method" name="getCrawlType" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="64" type="method" name="getCustomFetcher" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="68" type="method" name="getWorker" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="72" type="method" name="getCustomUserAgent" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="73" type="stmt" count="0"/>
      <line num="76" type="method" name="useSmartCrawler" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="80" type="method" name="useHeadlessBrowser" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="81" type="stmt" count="0"/>
      <line num="84" type="method" name="getCustomPrompt" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="85" type="stmt" count="0"/>
      <line num="88" type="method" name="toPuppeteer" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="89" type="stmt" count="0"/>
      <metrics loc="92" ncloc="92" classes="1" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="34" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/EndpointObject.php">
      <class name="App\Classes\ValueObjects\EndpointObject" namespace="global">
        <metrics complexity="6" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="12" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="17" type="method" name="getCrawlerSettingId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="21" type="method" name="getEndpoint" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="25" type="method" name="getDefaultCategories" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="29" type="method" name="getDefaultTopics" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="33" type="method" name="hasProxyImage" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <metrics loc="37" ncloc="37" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="12" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/EndpointWithCrawlerSettingObject.php">
      <class name="App\Classes\ValueObjects\EndpointWithCrawlerSettingObject" namespace="global">
        <metrics complexity="11" methods="11" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="22" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="15" type="method" name="getEndpointId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="19" type="method" name="getCrawlerSettingId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="23" type="method" name="getPublisherId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="27" type="method" name="getChannelId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="31" type="method" name="getHost" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="35" type="method" name="getEndpoint" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="39" type="method" name="getFrequency" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="43" type="method" name="getCustomUserAgent" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="47" type="method" name="getCustomPrompt" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="51" type="method" name="useHeadlessBrowser" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <metrics loc="55" ncloc="55" classes="1" methods="11" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="22" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/GeminiAiModelObject.php">
      <class name="App\Classes\ValueObjects\GeminiAiModelObject" namespace="global">
        <metrics complexity="5" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="25" coveredstatements="0" elements="30" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="21" type="method" name="getModelParameters" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="46" type="method" name="getModelName" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="50" type="method" name="getModelUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="54" type="method" name="getApiKey" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <metrics loc="58" ncloc="58" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="25" coveredstatements="0" elements="30" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/OpenAiModelObject.php">
      <class name="App\Classes\ValueObjects\OpenAiModelObject" namespace="global">
        <metrics complexity="5" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="12" coveredstatements="0" elements="17" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="21" type="method" name="getModelParameters" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="32" type="method" name="getModelName" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="36" type="method" name="getModelUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="40" type="method" name="getApiKey" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <metrics loc="44" ncloc="44" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="12" coveredstatements="0" elements="17" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/ParsedArticleObject.php">
      <class name="App\Classes\ValueObjects\ParsedArticleObject" namespace="global">
        <metrics complexity="16" methods="16" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="0" elements="32" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="24" type="method" name="getArticleId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="28" type="method" name="getTitle" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="32" type="method" name="getDescription" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="36" type="method" name="getFullContent" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="40" type="method" name="getAuthor" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="44" type="method" name="setPublishedDate" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="48" type="method" name="getPublishedDate" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="52" type="method" name="getModifiedDate" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="56" type="method" name="getUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="60" type="method" name="getCanonicalURL" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="64" type="method" name="getCoverImage" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="68" type="method" name="getCoverImageUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="72" type="method" name="getCoverImageCaption" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="73" type="stmt" count="0"/>
      <line num="76" type="method" name="getMedia" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="80" type="method" name="getContentMd5" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="81" type="stmt" count="0"/>
      <metrics loc="84" ncloc="84" classes="1" methods="16" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="0" elements="32" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Classes/ValueObjects/RawContentObject.php">
      <class name="App\Classes\ValueObjects\RawContentObject" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <line num="13" type="method" name="getRawContent" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <metrics loc="17" ncloc="17" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Console/Commands/FetchAndProcessArticlesByEndpointCommand.php">
      <class name="App\Console\Commands\FetchAndProcessArticlesByEndpointCommand" namespace="global">
        <metrics complexity="3" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="7" coveredelements="0"/>
      </class>
      <line num="17" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="23" type="method" name="handle" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <metrics loc="33" ncloc="33" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="7" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Console/Commands/FetchAndProcessArticlesByWorkerCommand.php">
      <class name="App\Console\Commands\FetchAndProcessArticlesByWorkerCommand" namespace="global">
        <metrics complexity="3" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="7" coveredelements="0"/>
      </class>
      <line num="17" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="23" type="method" name="handle" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <metrics loc="33" ncloc="33" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="7" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Console/Commands/PopulateCrawlerSettingCommand.php">
      <class name="App\Console\Commands\PopulateCrawlerSettingCommand" namespace="global">
        <metrics complexity="6" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="70" coveredstatements="0" elements="73" coveredelements="0"/>
      </class>
      <line num="19" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="26" type="method" name="handle" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="67" type="stmt" count="0"/>
      <line num="68" type="stmt" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="70" type="stmt" count="0"/>
      <line num="72" type="stmt" count="0"/>
      <line num="73" type="stmt" count="0"/>
      <line num="76" type="stmt" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="80" type="stmt" count="0"/>
      <line num="81" type="stmt" count="0"/>
      <line num="82" type="stmt" count="0"/>
      <line num="85" type="method" name="insertEndpoint" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="86" type="stmt" count="0"/>
      <line num="87" type="stmt" count="0"/>
      <line num="88" type="stmt" count="0"/>
      <line num="89" type="stmt" count="0"/>
      <line num="90" type="stmt" count="0"/>
      <line num="92" type="stmt" count="0"/>
      <line num="93" type="stmt" count="0"/>
      <line num="94" type="stmt" count="0"/>
      <line num="95" type="stmt" count="0"/>
      <line num="96" type="stmt" count="0"/>
      <line num="97" type="stmt" count="0"/>
      <line num="98" type="stmt" count="0"/>
      <line num="100" type="stmt" count="0"/>
      <line num="101" type="stmt" count="0"/>
      <line num="102" type="stmt" count="0"/>
      <line num="103" type="stmt" count="0"/>
      <line num="104" type="stmt" count="0"/>
      <line num="106" type="stmt" count="0"/>
      <line num="109" type="stmt" count="0"/>
      <line num="110" type="stmt" count="0"/>
      <line num="111" type="stmt" count="0"/>
      <metrics loc="114" ncloc="114" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="70" coveredstatements="0" elements="73" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Console/Kernel.php">
      <class name="App\Console\Kernel" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="schedule" visibility="protected" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="21" type="method" name="commands" visibility="protected" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <metrics loc="25" ncloc="18" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Enums/AiModels.php">
      <metrics loc="14" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Events/ArticleDataIsFetched.php">
      <class name="App\Events\ArticleDataIsFetched" namespace="global">
        <metrics complexity="11" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="0" elements="25" coveredelements="0"/>
      </class>
      <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="26" type="method" name="getPublisherId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="30" type="method" name="getChannelId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="34" type="method" name="getArticleData" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="38" type="method" name="isRss" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="42" type="method" name="getCustomPrompt" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="46" type="method" name="useHeadlessBrowser" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="50" type="method" name="getTopic" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="54" type="method" name="getPayload" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <metrics loc="65" ncloc="61" classes="1" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="0" elements="25" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Events/PubSubEvent.php">
      <metrics loc="12" ncloc="12" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/APIException.php">
      <class name="App\Exceptions\APIException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="9" type="stmt" count="0"/>
      <metrics loc="12" ncloc="12" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/AiClientException.php">
      <class name="App\Exceptions\AiClientException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="9" type="stmt" count="0"/>
      <metrics loc="12" ncloc="12" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/ArticleAdderException.php">
      <class name="App\Exceptions\ArticleAdderException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="9" type="stmt" count="0"/>
      <metrics loc="12" ncloc="12" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/BaseException.php">
      <class name="App\Exceptions\BaseException" namespace="global">
        <metrics complexity="7" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="18" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="21" type="method" name="getStatusCode" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="25" type="method" name="getErrorMessageCode" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="29" type="method" name="getLocalisedTitle" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="39" type="method" name="getLocalisedMessage" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <metrics loc="47" ncloc="45" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="18" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/FetcherCommandException.php">
      <class name="App\Exceptions\FetcherCommandException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="9" type="stmt" count="0"/>
      <metrics loc="12" ncloc="12" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/Handler.php">
      <class name="App\Exceptions\Handler" namespace="global">
        <metrics complexity="2" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="0" elements="11" coveredelements="0"/>
      </class>
      <line num="28" type="method" name="register" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <metrics loc="44" ncloc="36" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="0" elements="11" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/InternalServerErrorException.php">
      <class name="App\Exceptions\InternalServerErrorException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <metrics loc="14" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/ParseArticleException.php">
      <class name="App\Exceptions\ParseArticleException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="9" type="stmt" count="0"/>
      <metrics loc="12" ncloc="12" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/ResourceNotFoundException.php">
      <class name="App\Exceptions\ResourceNotFoundException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <metrics loc="14" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/ServiceException.php">
      <class name="App\Exceptions\ServiceException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="8" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="9" type="stmt" count="0"/>
      <metrics loc="12" ncloc="12" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Exceptions/UnauthorisedException.php">
      <class name="App\Exceptions\UnauthorisedException" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <metrics loc="14" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Helpers/ContentHelper.php">
      <class name="App\Helpers\ContentHelper" namespace="global">
        <metrics complexity="5" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="20" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="isContentRss" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="27" type="method" name="slugifyTitle" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="39" type="method" name="getCanonicalUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <metrics loc="45" ncloc="45" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="20" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Helpers/MediaHelper.php">
      <class name="App\Helpers\MediaHelper" namespace="global">
        <metrics complexity="12" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="50" coveredstatements="0" elements="54" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="isImageUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="21" type="method" name="isVideoUrl" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="28" type="method" name="getImageSize" visibility="public" complexity="7" crap="56" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="68" type="method" name="isMediaUrlValid" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="70" type="stmt" count="0"/>
      <line num="71" type="stmt" count="0"/>
      <line num="74" type="stmt" count="0"/>
      <line num="75" type="stmt" count="0"/>
      <line num="76" type="stmt" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="78" type="stmt" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="80" type="stmt" count="0"/>
      <line num="81" type="stmt" count="0"/>
      <line num="82" type="stmt" count="0"/>
      <line num="83" type="stmt" count="0"/>
      <line num="85" type="stmt" count="0"/>
      <line num="86" type="stmt" count="0"/>
      <line num="88" type="stmt" count="0"/>
      <line num="89" type="stmt" count="0"/>
      <line num="90" type="stmt" count="0"/>
      <metrics loc="94" ncloc="94" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="50" coveredstatements="0" elements="54" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Controllers/Controller.php">
      <class name="App\Http\Controllers\Controller" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="14" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Controllers/HealthController.php">
      <class name="App\Http\Controllers\HealthController" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="check" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <metrics loc="14" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Controllers/ParserAdderController.php">
      <class name="App\Http\Controllers\ParserAdderController" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="0" elements="17" coveredelements="0"/>
      </class>
      <line num="15" type="method" name="parseArticleData" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <metrics loc="35" ncloc="35" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="0" elements="17" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Kernel.php">
      <class name="App\Http\Kernel" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="69" ncloc="49" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/Authenticate.php">
      <class name="App\Http\Middleware\Authenticate" namespace="global">
        <metrics complexity="9" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="19" coveredstatements="0" elements="21" coveredelements="0"/>
      </class>
      <line num="20" type="method" name="handle" visibility="public" complexity="5" crap="30" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="40" type="method" name="verifyGoogleOidcJwt" visibility="private" complexity="4" crap="20" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <metrics loc="61" ncloc="58" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="19" coveredstatements="0" elements="21" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/EncryptCookies.php">
      <class name="App\Http\Middleware\EncryptCookies" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="19" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/PreventRequestsDuringMaintenance.php">
      <class name="App\Http\Middleware\PreventRequestsDuringMaintenance" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="19" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/TrimStrings.php">
      <class name="App\Http\Middleware\TrimStrings" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="21" ncloc="16" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/TrustHosts.php">
      <class name="App\Http\Middleware\TrustHosts" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="3" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="15" type="method" name="hosts" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <metrics loc="21" ncloc="16" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="3" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/TrustProxies.php">
      <class name="App\Http\Middleware\TrustProxies" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="30" ncloc="20" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/ValidateSignature.php">
      <class name="App\Http\Middleware\ValidateSignature" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="24" ncloc="19" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Middleware/VerifyCsrfToken.php">
      <class name="App\Http\Middleware\VerifyCsrfToken" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="19" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Http/Requests/ParseArticleDataRequest.php">
      <class name="App\Http\Requests\ParseArticleDataRequest" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="8" coveredstatements="0" elements="9" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="rules" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <line num="12" type="stmt" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <metrics loc="21" ncloc="21" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="8" coveredstatements="0" elements="9" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Listeners/PubSubEventHandler.php">
      <class name="App\Listeners\PubSubEventHandler" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="12" type="stmt" count="0"/>
      <line num="14" type="method" name="handle" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <metrics loc="18" ncloc="18" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/Article.php">
      <class name="App\Models\Article" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="channel" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <metrics loc="18" ncloc="18" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/ArticleWordCount.php">
      <class name="App\Models\ArticleWordCount" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/Channel.php">
      <class name="App\Models\Channel" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/Media.php">
      <class name="App\Models\Media" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/Prediction.php">
      <class name="App\Models\Prediction" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="article" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="18" type="method" name="publisher" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <metrics loc="22" ncloc="22" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/Publisher.php">
      <class name="App\Models\Publisher" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/PublisherCrawlerSetting.php">
      <class name="App\Models\PublisherCrawlerSetting" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/PublisherEndpoint.php">
      <class name="App\Models\PublisherEndpoint" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="crawlerSetting" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <metrics loc="18" ncloc="18" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/Thumbnail.php">
      <class name="App\Models\Thumbnail" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Models/Topic.php">
      <class name="App\Models\Topic" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/AiModels/AiModelClient.php">
      <class name="App\Modules\AiModels\AiModelClient" namespace="global">
        <metrics complexity="9" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="53" coveredstatements="0" elements="57" coveredelements="0"/>
      </class>
      <line num="22" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="30" type="method" name="ask" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="66" type="stmt" count="0"/>
      <line num="67" type="stmt" count="0"/>
      <line num="68" type="stmt" count="0"/>
      <line num="70" type="stmt" count="0"/>
      <line num="71" type="stmt" count="0"/>
      <line num="72" type="stmt" count="0"/>
      <line num="76" type="method" name="makeRequestWithRetry" visibility="private" complexity="3" crap="12" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="78" type="stmt" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="81" type="stmt" count="0"/>
      <line num="82" type="stmt" count="0"/>
      <line num="83" type="stmt" count="0"/>
      <line num="84" type="stmt" count="0"/>
      <line num="85" type="stmt" count="0"/>
      <line num="86" type="stmt" count="0"/>
      <line num="90" type="stmt" count="0"/>
      <line num="93" type="method" name="makeRequest" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="95" type="stmt" count="0"/>
      <line num="96" type="stmt" count="0"/>
      <line num="97" type="stmt" count="0"/>
      <line num="98" type="stmt" count="0"/>
      <line num="99" type="stmt" count="0"/>
      <line num="100" type="stmt" count="0"/>
      <line num="101" type="stmt" count="0"/>
      <line num="103" type="stmt" count="0"/>
      <line num="104" type="stmt" count="0"/>
      <line num="105" type="stmt" count="0"/>
      <metrics loc="109" ncloc="109" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="53" coveredstatements="0" elements="57" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/AiModels/GeminiAccessTokenProvider.php">
      <class name="App\Modules\AiModels\GeminiAccessTokenProvider" namespace="global">
        <metrics complexity="2" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="0" elements="11" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="execute" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="12" type="stmt" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <metrics loc="27" ncloc="27" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="0" elements="11" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/AiModels/Services/LogsAiModelEstimatedCost.php">
      <class name="App\Modules\AiModels\Services\LogsAiModelEstimatedCost" namespace="global">
        <metrics complexity="5" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="18" coveredstatements="0" elements="23" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="execute" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="12" type="stmt" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="22" type="method" name="calculateThinkingCost" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="28" type="method" name="calculateFinalResultCost" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="34" type="method" name="estimateTotalCost" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="43" type="method" name="formatCost" visibility="protected" complexity="1" crap="2" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <metrics loc="47" ncloc="47" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="18" coveredstatements="0" elements="23" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/Fetcher/Logics/FetchAndProcessEndpointFoWorkerLogic.php">
      <class name="App\Modules\Fetcher\Logics\FetchAndProcessEndpointFoWorkerLogic" namespace="global">
        <metrics complexity="5" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="21" coveredstatements="0" elements="23" coveredelements="0"/>
      </class>
      <line num="17" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="27" type="method" name="execute" visibility="public" complexity="4" crap="20" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <metrics loc="57" ncloc="57" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="21" coveredstatements="0" elements="23" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/Fetcher/Logics/FetchAndProcessEndpointForLocalFetcherLogic.php">
      <class name="App\Modules\Fetcher\Logics\FetchAndProcessEndpointForLocalFetcherLogic" namespace="global">
        <metrics complexity="4" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="22" coveredelements="0"/>
      </class>
      <line num="17" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="27" type="method" name="execute" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <metrics loc="54" ncloc="54" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="22" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/Fetcher/Services/ExtractsRssItemsFromRawContent.php">
      <class name="App\Modules\Fetcher\Services\ExtractsRssItemsFromRawContent" namespace="global">
        <metrics complexity="13" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="27" coveredstatements="0" elements="31" coveredelements="0"/>
      </class>
      <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="25" type="method" name="execute" visibility="public" complexity="5" crap="30" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="46" type="method" name="extractLinkFromItem" visibility="private" complexity="5" crap="30" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="66" type="method" name="getItemElements" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="67" type="stmt" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="70" type="stmt" count="0"/>
      <line num="71" type="stmt" count="0"/>
      <metrics loc="74" ncloc="71" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="27" coveredstatements="0" elements="31" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/Fetcher/Services/PassesArticleDataToParserAdder.php">
      <class name="App\Modules\Fetcher\Services\PassesArticleDataToParserAdder" namespace="global">
        <metrics complexity="5" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="24" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="18" type="method" name="execute" visibility="public" complexity="4" crap="20" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <metrics loc="50" ncloc="50" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="24" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/Fetcher/Services/RetrievesArticleDataFromRawContent.php">
      <class name="App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent" namespace="global">
        <metrics complexity="7" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="14" coveredstatements="0" elements="18" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="22" type="method" name="execute" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="33" type="method" name="extractJsonArray" visibility="private" complexity="3" crap="12" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="46" type="method" name="getNewArticlesFromLinks" visibility="private" complexity="1" crap="2" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <metrics loc="52" ncloc="52" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="14" coveredstatements="0" elements="18" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Logics/ParseArticleDataLogic.php">
      <class name="App\Modules\ParserAdder\Logics\ParseArticleDataLogic" namespace="global">
        <metrics complexity="7" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="23" coveredelements="0"/>
      </class>
      <line num="19" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="28" type="method" name="execute" visibility="public" complexity="5" crap="30" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="55" type="method" name="extractJson" visibility="private" complexity="1" crap="2" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <metrics loc="62" ncloc="62" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="23" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Logics/PopulateArticleDataIntoDatabaseLogic.php">
      <class name="App\Modules\ParserAdder\Logics\PopulateArticleDataIntoDatabaseLogic" namespace="global">
        <metrics complexity="7" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="34" coveredstatements="0" elements="36" coveredelements="0"/>
      </class>
      <line num="26" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="40" type="method" name="execute" visibility="public" complexity="6" crap="42" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="66" type="stmt" count="0"/>
      <line num="67" type="stmt" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="70" type="stmt" count="0"/>
      <line num="72" type="stmt" count="0"/>
      <line num="73" type="stmt" count="0"/>
      <line num="75" type="stmt" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="78" type="stmt" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="80" type="stmt" count="0"/>
      <line num="83" type="stmt" count="0"/>
      <line num="85" type="stmt" count="0"/>
      <line num="86" type="stmt" count="0"/>
      <metrics loc="91" ncloc="91" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="34" coveredstatements="0" elements="36" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/CreatesArticle.php">
      <class name="App\Modules\ParserAdder\Services\CreatesArticle" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="17" type="method" name="execute" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <metrics loc="21" ncloc="21" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/CreatesArticleMedia.php">
      <class name="App\Modules\ParserAdder\Services\CreatesArticleMedia" namespace="global">
        <metrics complexity="17" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="57" coveredstatements="0" elements="62" coveredelements="0"/>
      </class>
      <line num="15" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="25" type="method" name="execute" visibility="public" complexity="4" crap="20" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="45" type="method" name="handleMedia" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="56" type="method" name="insertThumbnail" visibility="private" complexity="5" crap="30" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="66" type="stmt" count="0"/>
      <line num="67" type="stmt" count="0"/>
      <line num="70" type="stmt" count="0"/>
      <line num="71" type="stmt" count="0"/>
      <line num="74" type="stmt" count="0"/>
      <line num="75" type="stmt" count="0"/>
      <line num="76" type="stmt" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="78" type="stmt" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="80" type="stmt" count="0"/>
      <line num="81" type="stmt" count="0"/>
      <line num="82" type="stmt" count="0"/>
      <line num="85" type="method" name="insertMedia" visibility="private" complexity="5" crap="30" count="0"/>
      <line num="87" type="stmt" count="0"/>
      <line num="88" type="stmt" count="0"/>
      <line num="91" type="stmt" count="0"/>
      <line num="92" type="stmt" count="0"/>
      <line num="93" type="stmt" count="0"/>
      <line num="96" type="stmt" count="0"/>
      <line num="97" type="stmt" count="0"/>
      <line num="100" type="stmt" count="0"/>
      <line num="101" type="stmt" count="0"/>
      <line num="103" type="stmt" count="0"/>
      <line num="104" type="stmt" count="0"/>
      <line num="105" type="stmt" count="0"/>
      <line num="106" type="stmt" count="0"/>
      <line num="109" type="stmt" count="0"/>
      <line num="110" type="stmt" count="0"/>
      <line num="111" type="stmt" count="0"/>
      <line num="112" type="stmt" count="0"/>
      <line num="113" type="stmt" count="0"/>
      <line num="114" type="stmt" count="0"/>
      <line num="115" type="stmt" count="0"/>
      <line num="116" type="stmt" count="0"/>
      <metrics loc="119" ncloc="119" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="57" coveredstatements="0" elements="62" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/CreatesPrediction.php">
      <class name="App\Modules\ParserAdder\Services\CreatesPrediction" namespace="global">
        <metrics complexity="3" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="25" coveredstatements="0" elements="27" coveredelements="0"/>
      </class>
      <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="17" type="method" name="execute" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <metrics loc="46" ncloc="46" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="25" coveredstatements="0" elements="27" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/GeneratesUniqueIdForArticle.php">
      <class name="App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle" namespace="global">
        <metrics complexity="3" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="0" elements="12" coveredelements="0"/>
      </class>
      <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="17" type="method" name="execute" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <metrics loc="33" ncloc="33" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="0" elements="12" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/PopulatesArticleWordCount.php">
      <class name="App\Modules\ParserAdder\Services\PopulatesArticleWordCount" namespace="global">
        <metrics complexity="3" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="13" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="15" type="method" name="execute" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <metrics loc="30" ncloc="30" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="13" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/RetrievesImageProxyLinks.php">
      <class name="App\Modules\ParserAdder\Services\RetrievesImageProxyLinks" namespace="global">
        <metrics complexity="5" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="18" coveredstatements="0" elements="21" coveredelements="0"/>
      </class>
      <line num="10" type="method" name="execute" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="11" type="stmt" count="0"/>
      <line num="12" type="stmt" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="22" type="method" name="generateUrlWithSecret" visibility="private" complexity="1" crap="2" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="35" type="method" name="generateUrlWithoutSecret" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <metrics loc="47" ncloc="46" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="18" coveredstatements="0" elements="21" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/SanitizesParsedContent.php">
      <class name="App\Modules\ParserAdder\Services\SanitizesParsedContent" namespace="global">
        <metrics complexity="21" methods="8" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="74" coveredstatements="0" elements="82" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="20" type="method" name="execute" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="32" type="method" name="addGaTrackingIntoContent" visibility="protected" complexity="3" crap="12" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="62" type="method" name="sanitizeDescription" visibility="protected" complexity="2" crap="6" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="68" type="stmt" count="0"/>
      <line num="71" type="method" name="sanitizeContent" visibility="protected" complexity="1" crap="2" count="0"/>
      <line num="72" type="stmt" count="0"/>
      <line num="74" type="stmt" count="0"/>
      <line num="75" type="stmt" count="0"/>
      <line num="76" type="stmt" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="82" type="method" name="addFirstImage" visibility="protected" complexity="3" crap="12" count="0"/>
      <line num="83" type="stmt" count="0"/>
      <line num="84" type="stmt" count="0"/>
      <line num="85" type="stmt" count="0"/>
      <line num="86" type="stmt" count="0"/>
      <line num="88" type="stmt" count="0"/>
      <line num="89" type="stmt" count="0"/>
      <line num="90" type="stmt" count="0"/>
      <line num="91" type="stmt" count="0"/>
      <line num="92" type="stmt" count="0"/>
      <line num="93" type="stmt" count="0"/>
      <line num="94" type="stmt" count="0"/>
      <line num="95" type="stmt" count="0"/>
      <line num="96" type="stmt" count="0"/>
      <line num="97" type="stmt" count="0"/>
      <line num="98" type="stmt" count="0"/>
      <line num="100" type="stmt" count="0"/>
      <line num="103" type="stmt" count="0"/>
      <line num="106" type="method" name="insertAdsIntoContent" visibility="protected" complexity="8" crap="72" count="0"/>
      <line num="107" type="stmt" count="0"/>
      <line num="108" type="stmt" count="0"/>
      <line num="109" type="stmt" count="0"/>
      <line num="111" type="stmt" count="0"/>
      <line num="112" type="stmt" count="0"/>
      <line num="115" type="stmt" count="0"/>
      <line num="116" type="stmt" count="0"/>
      <line num="119" type="stmt" count="0"/>
      <line num="120" type="stmt" count="0"/>
      <line num="122" type="stmt" count="0"/>
      <line num="123" type="stmt" count="0"/>
      <line num="124" type="stmt" count="0"/>
      <line num="127" type="stmt" count="0"/>
      <line num="128" type="stmt" count="0"/>
      <line num="129" type="stmt" count="0"/>
      <line num="130" type="stmt" count="0"/>
      <line num="132" type="stmt" count="0"/>
      <line num="133" type="stmt" count="0"/>
      <line num="137" type="stmt" count="0"/>
      <line num="140" type="method" name="getPath" visibility="protected" complexity="1" crap="2" count="0"/>
      <line num="141" type="stmt" count="0"/>
      <metrics loc="144" ncloc="144" classes="1" methods="8" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="74" coveredstatements="0" elements="82" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/UpdatesArticle.php">
      <class name="App\Modules\ParserAdder\Services\UpdatesArticle" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="11" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="16" type="method" name="execute" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <metrics loc="20" ncloc="20" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Modules/ParserAdder/Services/ValidatesParsedContent.php">
      <class name="App\Modules\ParserAdder\Services\ValidatesParsedContent" namespace="global">
        <metrics complexity="25" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="50" coveredstatements="0" elements="59" coveredelements="0"/>
      </class>
      <line num="15" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="21" type="method" name="execute" visibility="public" complexity="4" crap="20" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="46" type="method" name="validateArticleId" visibility="private" complexity="1" crap="2" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="50" type="method" name="validateDescription" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="58" type="method" name="validateAuthor" visibility="private" complexity="3" crap="12" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="68" type="method" name="validatePublishedDate" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="72" type="method" name="validateModifiedDate" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="73" type="stmt" count="0"/>
      <line num="76" type="method" name="validateCoverImage" visibility="private" complexity="7" crap="56" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="78" type="stmt" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="80" type="stmt" count="0"/>
      <line num="81" type="stmt" count="0"/>
      <line num="84" type="stmt" count="0"/>
      <line num="85" type="stmt" count="0"/>
      <line num="86" type="stmt" count="0"/>
      <line num="87" type="stmt" count="0"/>
      <line num="88" type="stmt" count="0"/>
      <line num="89" type="stmt" count="0"/>
      <line num="90" type="stmt" count="0"/>
      <line num="95" type="stmt" count="0"/>
      <line num="98" type="method" name="validateMedia" visibility="private" complexity="3" crap="12" count="0"/>
      <line num="99" type="stmt" count="0"/>
      <line num="101" type="stmt" count="0"/>
      <line num="102" type="stmt" count="0"/>
      <line num="103" type="stmt" count="0"/>
      <line num="106" type="stmt" count="0"/>
      <line num="107" type="stmt" count="0"/>
      <line num="108" type="stmt" count="0"/>
      <line num="109" type="stmt" count="0"/>
      <line num="112" type="stmt" count="0"/>
      <metrics loc="115" ncloc="115" classes="1" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="50" coveredstatements="0" elements="59" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Providers/AppServiceProvider.php">
      <class name="App\Providers\AppServiceProvider" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="register" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="20" type="method" name="boot" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <metrics loc="24" ncloc="18" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Providers/AuthServiceProvider.php">
      <class name="App\Providers\AuthServiceProvider" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="23" type="method" name="boot" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <metrics loc="27" ncloc="18" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Providers/BroadcastServiceProvider.php">
      <class name="App\Providers\BroadcastServiceProvider" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="3" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="boot" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <metrics loc="20" ncloc="17" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="3" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Providers/EventServiceProvider.php">
      <class name="App\Providers\EventServiceProvider" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="21" ncloc="16" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Providers/RouteServiceProvider.php">
      <class name="App\Providers\RouteServiceProvider" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="6" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="boot" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <metrics loc="22" ncloc="19" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="0" elements="6" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/ArticleRepository.php">
      <class name="App\Repositories\ArticleRepository" namespace="global">
        <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="47" coveredstatements="0" elements="50" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="18" type="method" name="createArticleFromObject" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="52" type="method" name="updateArticleFromObject" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="66" type="stmt" count="0"/>
      <line num="67" type="stmt" count="0"/>
      <metrics loc="70" ncloc="67" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="47" coveredstatements="0" elements="50" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/ArticleWordCountRepository.php">
      <class name="App\Repositories\ArticleWordCountRepository" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <metrics loc="17" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/BaseRepository.php">
      <class name="App\Repositories\BaseRepository" namespace="global">
        <metrics complexity="23" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="38" coveredstatements="0" elements="55" coveredelements="0"/>
      </class>
      <line num="23" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="30" type="method" name="all" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="37" type="method" name="find" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="46" type="method" name="findBy" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="58" type="method" name="findWhere" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="65" type="method" name="findAllWhere" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="66" type="stmt" count="0"/>
      <line num="68" type="stmt" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="72" type="stmt" count="0"/>
      <line num="78" type="method" name="findAllWhereIn" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="79" type="stmt" count="0"/>
      <line num="85" type="method" name="findAllBasedOnCriteria" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="87" type="stmt" count="0"/>
      <line num="90" type="stmt" count="0"/>
      <line num="91" type="stmt" count="0"/>
      <line num="95" type="stmt" count="0"/>
      <line num="97" type="stmt" count="0"/>
      <line num="105" type="method" name="getLazyCollectionBasedOnCriteria" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="106" type="stmt" count="0"/>
      <line num="107" type="stmt" count="0"/>
      <line num="108" type="stmt" count="0"/>
      <line num="111" type="stmt" count="0"/>
      <line num="114" type="method" name="getBuilderBasedOnCriteria" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="115" type="stmt" count="0"/>
      <line num="116" type="stmt" count="0"/>
      <line num="117" type="stmt" count="0"/>
      <line num="121" type="stmt" count="0"/>
      <line num="129" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="130" type="stmt" count="0"/>
      <line num="131" type="stmt" count="0"/>
      <line num="132" type="stmt" count="0"/>
      <line num="134" type="stmt" count="0"/>
      <line num="142" type="method" name="update" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="143" type="stmt" count="0"/>
      <line num="146" type="method" name="delete" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="148" type="stmt" count="0"/>
      <line num="149" type="stmt" count="0"/>
      <line num="150" type="stmt" count="0"/>
      <line num="154" type="method" name="make" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="155" type="stmt" count="0"/>
      <line num="161" type="method" name="cursor" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="162" type="stmt" count="0"/>
      <line num="168" type="method" name="getQueryFromCriteria" visibility="protected" complexity="1" crap="2" count="0"/>
      <line num="169" type="stmt" count="0"/>
      <line num="172" type="method" name="getNewQuery" visibility="private" complexity="1" crap="2" count="0"/>
      <line num="173" type="stmt" count="0"/>
      <metrics loc="176" ncloc="122" classes="1" methods="17" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="38" coveredstatements="0" elements="55" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/MediaRepository.php">
      <class name="App\Repositories\MediaRepository" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <metrics loc="17" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/PredictionsRepository.php">
      <class name="App\Repositories\PredictionsRepository" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="0" elements="6" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="18" type="method" name="getArticleDataWithPredictionData" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <metrics loc="24" ncloc="21" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="0" elements="6" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/PublisherCrawlerSettingRepository.php">
      <class name="App\Repositories\PublisherCrawlerSettingRepository" namespace="global">
        <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="35" coveredstatements="0" elements="38" coveredelements="0"/>
      </class>
      <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="18" type="method" name="createFromObject" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="20" type="stmt" count="0"/>
      <line num="21" type="stmt" count="0"/>
      <line num="22" type="stmt" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="39" type="method" name="updateFromObject" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <metrics loc="58" ncloc="55" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="35" coveredstatements="0" elements="38" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/PublisherEndpointRepository.php">
      <class name="App\Repositories\PublisherEndpointRepository" namespace="global">
        <metrics complexity="6" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="30" coveredelements="0"/>
      </class>
      <line num="18" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="22" type="method" name="getCrawlerSettingsWithEndpointsByWorkerId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="28" type="method" name="getOneCrawlerSettingWithEndpointByEndpointId" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="34" type="method" name="getHasProxyImage" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="40" type="method" name="createFromObject" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="50" type="method" name="updateFromObject" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <metrics loc="60" ncloc="57" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="30" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/PublisherRepository.php">
      <class name="App\Repositories\PublisherRepository" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <metrics loc="17" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/ThumbnailRepository.php">
      <class name="App\Repositories\ThumbnailRepository" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <metrics loc="17" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Repositories/TopicsRepository.php">
      <class name="App\Repositories\TopicsRepository" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </class>
      <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <metrics loc="17" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Services/BypassCloudflareService.php">
      <class name="App\Services\BypassCloudflareService" namespace="global">
        <metrics complexity="6" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="21" coveredstatements="0" elements="24" coveredelements="0"/>
      </class>
      <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="24" type="method" name="getRawHtmlOrRss" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="45" type="method" name="makeRequest" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <metrics loc="62" ncloc="54" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="21" coveredstatements="0" elements="24" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Services/GoogleCloudPubSubService.php">
      <class name="App\Services\GoogleCloudPubSubService" namespace="global">
        <metrics complexity="2" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="32" ncloc="29" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Services/HeadlessBrowserService.php">
      <class name="App\Services\HeadlessBrowserService" namespace="global">
        <metrics complexity="6" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="21" coveredstatements="0" elements="24" coveredelements="0"/>
      </class>
      <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="17" type="stmt" count="0"/>
      <line num="24" type="method" name="getRawHtmlOrRss" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="45" type="method" name="makeRequest" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <metrics loc="62" ncloc="54" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="21" coveredstatements="0" elements="24" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Services/NewswavOutboxClient.php">
      <class name="App\Services\NewswavOutboxClient" namespace="global">
        <metrics complexity="14" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="80" coveredstatements="0" elements="85" coveredelements="0"/>
      </class>
      <line num="18" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="19" type="stmt" count="0"/>
      <line num="21" type="method" name="emitMessage" visibility="public" complexity="2" crap="6" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="36" type="stmt" count="0"/>
      <line num="37" type="stmt" count="0"/>
      <line num="38" type="stmt" count="0"/>
      <line num="39" type="stmt" count="0"/>
      <line num="40" type="stmt" count="0"/>
      <line num="41" type="stmt" count="0"/>
      <line num="42" type="stmt" count="0"/>
      <line num="43" type="stmt" count="0"/>
      <line num="44" type="stmt" count="0"/>
      <line num="45" type="stmt" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="54" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <line num="58" type="stmt" count="0"/>
      <line num="59" type="stmt" count="0"/>
      <line num="60" type="stmt" count="0"/>
      <line num="61" type="stmt" count="0"/>
      <line num="62" type="stmt" count="0"/>
      <line num="63" type="stmt" count="0"/>
      <line num="64" type="stmt" count="0"/>
      <line num="65" type="stmt" count="0"/>
      <line num="66" type="stmt" count="0"/>
      <line num="67" type="stmt" count="0"/>
      <line num="68" type="stmt" count="0"/>
      <line num="69" type="stmt" count="0"/>
      <line num="70" type="stmt" count="0"/>
      <line num="71" type="stmt" count="0"/>
      <line num="72" type="stmt" count="0"/>
      <line num="73" type="stmt" count="0"/>
      <line num="74" type="stmt" count="0"/>
      <line num="75" type="stmt" count="0"/>
      <line num="76" type="stmt" count="0"/>
      <line num="77" type="stmt" count="0"/>
      <line num="85" type="method" name="makeRequest" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="87" type="stmt" count="0"/>
      <line num="88" type="stmt" count="0"/>
      <line num="89" type="stmt" count="0"/>
      <line num="90" type="stmt" count="0"/>
      <line num="91" type="stmt" count="0"/>
      <line num="92" type="stmt" count="0"/>
      <line num="93" type="stmt" count="0"/>
      <line num="95" type="stmt" count="0"/>
      <line num="96" type="stmt" count="0"/>
      <line num="97" type="stmt" count="0"/>
      <line num="101" type="method" name="extractKey" visibility="private" complexity="7" crap="56" count="0"/>
      <line num="103" type="stmt" count="0"/>
      <line num="104" type="stmt" count="0"/>
      <line num="105" type="stmt" count="0"/>
      <line num="106" type="stmt" count="0"/>
      <line num="107" type="stmt" count="0"/>
      <line num="109" type="stmt" count="0"/>
      <line num="113" type="stmt" count="0"/>
      <line num="114" type="stmt" count="0"/>
      <line num="117" type="stmt" count="0"/>
      <line num="118" type="stmt" count="0"/>
      <line num="119" type="stmt" count="0"/>
      <line num="121" type="stmt" count="0"/>
      <line num="125" type="method" name="calculateAspectRatio" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="126" type="stmt" count="0"/>
      <line num="127" type="stmt" count="0"/>
      <line num="128" type="stmt" count="0"/>
      <line num="130" type="stmt" count="0"/>
      <line num="132" type="stmt" count="0"/>
      <line num="133" type="stmt" count="0"/>
      <line num="135" type="stmt" count="0"/>
      <metrics loc="138" ncloc="133" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="80" coveredstatements="0" elements="85" coveredelements="0"/>
    </file>
    <file name="/Users/<USER>/Repositories/newswav-smart-crawler/src/app/Services/ParserAdderClient.php">
      <class name="App\Services\ParserAdderClient" namespace="global">
        <metrics complexity="6" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="25" coveredstatements="0" elements="28" coveredelements="0"/>
      </class>
      <line num="15" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="21" type="method" name="postArticleDataToParserAdderService" visibility="public" complexity="3" crap="12" count="0"/>
      <line num="23" type="stmt" count="0"/>
      <line num="24" type="stmt" count="0"/>
      <line num="25" type="stmt" count="0"/>
      <line num="26" type="stmt" count="0"/>
      <line num="27" type="stmt" count="0"/>
      <line num="28" type="stmt" count="0"/>
      <line num="29" type="stmt" count="0"/>
      <line num="30" type="stmt" count="0"/>
      <line num="31" type="stmt" count="0"/>
      <line num="32" type="stmt" count="0"/>
      <line num="33" type="stmt" count="0"/>
      <line num="34" type="stmt" count="0"/>
      <line num="35" type="stmt" count="0"/>
      <line num="44" type="method" name="makeRequest" visibility="private" complexity="2" crap="6" count="0"/>
      <line num="46" type="stmt" count="0"/>
      <line num="47" type="stmt" count="0"/>
      <line num="48" type="stmt" count="0"/>
      <line num="49" type="stmt" count="0"/>
      <line num="50" type="stmt" count="0"/>
      <line num="51" type="stmt" count="0"/>
      <line num="52" type="stmt" count="0"/>
      <line num="53" type="stmt" count="0"/>
      <line num="55" type="stmt" count="0"/>
      <line num="56" type="stmt" count="0"/>
      <line num="57" type="stmt" count="0"/>
      <metrics loc="61" ncloc="53" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="25" coveredstatements="0" elements="28" coveredelements="0"/>
    </file>
    <metrics files="101" loc="4245" ncloc="4012" classes="77" methods="267" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1241" coveredstatements="0" elements="1508" coveredelements="0"/>
  </project>
</coverage>
